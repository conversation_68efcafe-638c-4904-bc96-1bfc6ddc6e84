{"__meta": {"id": "Xd7fe281beec9241be9826fc3daa66381", "datetime": "2025-06-16 08:50:47", "utime": **********.737017, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063846.384904, "end": **********.737054, "duration": 1.3521502017974854, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1750063846.384904, "relative_start": 0, "end": **********.583379, "relative_end": **********.583379, "duration": 1.1984751224517822, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.583406, "relative_start": 1.1985020637512207, "end": **********.737057, "relative_end": 2.86102294921875e-06, "duration": 0.15365099906921387, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43894736, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01995, "accumulated_duration_str": "19.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.670081, "duration": 0.01866, "duration_str": "18.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.534}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7003188, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 93.534, "width_percent": 6.466}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1517679517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1517679517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1717189284 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063844760%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdjSHM1SlovSlpkcDVDcG03eC9DeUE9PSIsInZhbHVlIjoiT2V5NFp2U1RBcVhORTNFUVVPWWROcklMcmxWOVp2Z00rY2ZqTmdxWFZMUlVrcXhlQ2Q3WnZZRk52alFPYkhmemZHSGp4MG11L05QbHZ5SksvMEJTejRZNDRUR1krcE1uTW1xc0lnT00xdGZKbEVQWFpkWVVWRUtsU3FuTTlQOXBLK1VIYWpCSVZKeFNsOWg1Y3drRnJUdW9YZ2FDQWVFU21wcVVLRlZFR0RVWVViWUU5R2x2N1VZS3ZpbFRMVTF5QXpXaU9EdGlzd2pSZFRSQnFVaElNSzY3ZHcvV2kvcFhWNXVCTWZEY3FNMXNzL1kwaEhPZ25UWDR1Qk9BcjZqVnZMQzJLRmtJZzM3Zm4xcjRDaFlvUmZaOUg3SGk5ZlNjTU5kTi9sWWZwbjBFUHZUbUgxeW1QcTVmMXIwQnBZditYSnR6V3BDYUxIWWJHbEMxMUQ2am9KelRTY3VPS0hsWXFPRkpCSDIwRGM1MkxlRTFOWXJuYW91Nm1pVUNnREZOSmJUTDdGTmQwZWU5a1p3K3dHOG80Y3BjQSswTC92V2g1bDc3YnUyUjNETXFqQkx6T0FxT2xTV0ZqTGc5S3lpMXBoV1dFc0FkUGJWbitJY1UyRG83QmhzL3VDVkErT1ZtTDNDWExNWTNQcHc1Sml1UE9NYXdKSXdBL0RpQnVwU2QiLCJtYWMiOiIxOWJmNTQ5OTk3MzNhN2RhMjQ3ZjJhYWU2YTgwYTNhNzA0ODRiOGMzODE3NWZkMzljMmMzOWI2ZGMwMmQwYjAzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZlMUxKYld5d0FXWFN3OTBBQXhrT0E9PSIsInZhbHVlIjoibTBac2dGU29kQlpHOTJiVFpFb2pXMDh1WmUwRHVma2tGTCs0RkM4SVFoaXVKTnNsL0NVdFJ3eUptQXkyVG1aUjZ3Y003bS81U3NaRFBnSUJmR2VrUXRwUHQ0bEdwQ1puV0RMeDNMUzZtWnFpZE1CUWQ5VjRrZDBGMlplcUthVU9rMkt1WUVpcVdiVk9BZ3g4bTNUdHY1YUtnSU9QUXBPNmwyTUY4bmRobDFCL216RVFPeXBWOW5DeTBBZzl6cXBpL3U1dlpIbWRpcFlxcFJmNElpSVIyNmw2VThxR1d6bVc0Y0pNY2pTTnRzR3hEdmExbno1eUZoODdiY21xcjgxUDJWMk5QcHFKc29OZ2FSOU4zTW5nTVFVVC9WbjEra0VZR2VIK3M2QmJsQVdpY2EwM0pVUWlBaC9hKzFldVI4TGxtMjZvT01RbXY3ZHlZT2JNQ292Qys4QTZjUlZ2NXBmL3I0cVVMR0NtaGFtSHhGcFFIc21mVjJva1JxM09TdFpoNnR2ZEdKNU1rd1BpSEh0WFRHVE1aYzFhTHUxb3VDZ1cvOTRyQWxiSVlxZ25WQkhkTGhiTnQ4ZGhKbnZ3RlV5NGJJYUg0V2xHcHRSYzVJd3VxMDA0VlcvQ0wrUUhqaEs0bElQRytZWXF5dFBVeXhIWUZVNmFqRXY1NzVWUWFyWU8iLCJtYWMiOiI4ZTgyMjg0ZGVmZTA3YTRlZTFlYzIzZmZjNWZmYjUyMjBkMzhhY2Y4NDc4MzcwN2UwMWM0NjFhZjI3YmYyZjgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1717189284\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-886678334 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xy8o1OLZEDaYyV6WX9XzzYzbw5zHFfm46SvuTNQ8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886678334\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-41708604 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVESWpoZHU4ZnhCY213UEZvK3lvZ0E9PSIsInZhbHVlIjoiUDRKcTZxeTlwblVtbWZUdzhGQUdLLzNGanM1eXZCM3hHUjRmL0w5WnZyc0tKR1pYVkJnQzFiVWRMYXdGa2ZlR3FPY280R1JleSsrZXZVMmxaVlBiT3orVVJaVjdkWDZRaE44Rm5RdnJJa29QSXRZMktML1BuU0tZei9BV3NRMlNEdlpCa1k5YzlGeXFxczNVdXBlUWpObUZqNENGVk12d1BmSlZyQ0UzUWhROXFrWTkyN3Y4dlRjNms1bGR3WGlSL1F3Y2lDUkJxdnE5RGRLVlpKc3VKd2h3TnFNUmRNeEZJSkl5R1hKWU1GRWtQUVpmQjBhTjl1OXpnbGtkN2paTjRpZTFFMWY0dnhsYWFRY0J3ZzUvaXVGNGNHNkRRS0J5MnZSSGdLOVgxcDVKdHk2Q2JTNmNUeTNXU2NXSGlocHZlVEFCQmU3Wmd0aGtPU3FMbDNFZ1NIak4zZ1BzSk5YYUkvMFZMYkNnbUFsUythemtQR1FIZ1l1LzUxOXIyTW9vTDhLZFZoYTBPZ0s5N0wzN3VjdjJRc1FTcWFmZCtNeVFxL1lIdkU5RjBrSEp1WjZWMkdJK05lcHNsQytXeFMzRE5yUTcwdFlDQ1MxWTlaVWxVYlBiNHNKV3dvUFZpM1NJM3U2U0dwbDhGOE14UUhLSzVFcVVXcS9yN1JXU3FmeXoiLCJtYWMiOiIyODc5MWFlNjRlNTJjOTlmNjdhYTlmZjhjZWY4ZTE3NTI2ODExYWRmY2NiOTAzOTUwZGE0NTVjOWFkNTU0OWI0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InFlV0QrQTBZTm9McEJQRXZtZHNUOEE9PSIsInZhbHVlIjoiTXpIbWYzZVRYalJFSk0yNVBVMFJEOHU5dmZreFpQK1NVSTc4MEhpcmNVbzVNRVR2TlF4NkVJdjdkUkd0QXlMY01SRG1HbEVRT3FCZXdzZmF5Y0ViNEZGNmpINTVyMGNNeGJac3lqU1lPOGNIU0xjcG9yR1JCWXJiaEhRYWljWnlBeVMzQVBZNE56NzlSb2Y5RGIxclg2aFoySERIZFJ4Ymw0S3VraUpaMU9XTGFyb1NVRWJPSFZLQkdNSXM5NHczMk01Q1VmRHRjeWRkek9yWHBBVGMwb2luQjhKbVhhcXUwcEM5ZStvOXIwbmsvSi9TUGVGdTJXMVpVQ01YMVpUaHVEMHdqR2E2eU5MMENhZllJdGFWaFJpN1p2MDM5Rnpmb1hrY0NpK0tOaHBYWEZ3cmQwbUxIWGo4bXhheHVpUzhoUzd2MHcyVnp4cFRjemN5emlJNk5kN1QzVU96WHlheTNHMXdpUExiSmtMVnNCVUxuSWJ3MCt2ZWNyV3pKTDhOVGkrc0R5cklJUnBUSGsrMDBjYzB4L1RGRFhEL25xT3VUcGtOcTVUVjkrdnZGUjNSN2xJUGZGcEFheXowQjdObGFYeWhlYUZyVWZ3cHcxZzkzWllicU44ZnNZSXBhajVhWkdjc3FoZXJLRlFzSGI4OEduNUQ0Qk9nOWl2bU5yNGMiLCJtYWMiOiIyNzAzM2FhNDA1YjYyM2NhNzk3OGUyZDViYzc3NzI0YWFjOWVhMGQ1ZWZlNDEyN2JlNzk1N2IwMjQwMDg5MTRiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVESWpoZHU4ZnhCY213UEZvK3lvZ0E9PSIsInZhbHVlIjoiUDRKcTZxeTlwblVtbWZUdzhGQUdLLzNGanM1eXZCM3hHUjRmL0w5WnZyc0tKR1pYVkJnQzFiVWRMYXdGa2ZlR3FPY280R1JleSsrZXZVMmxaVlBiT3orVVJaVjdkWDZRaE44Rm5RdnJJa29QSXRZMktML1BuU0tZei9BV3NRMlNEdlpCa1k5YzlGeXFxczNVdXBlUWpObUZqNENGVk12d1BmSlZyQ0UzUWhROXFrWTkyN3Y4dlRjNms1bGR3WGlSL1F3Y2lDUkJxdnE5RGRLVlpKc3VKd2h3TnFNUmRNeEZJSkl5R1hKWU1GRWtQUVpmQjBhTjl1OXpnbGtkN2paTjRpZTFFMWY0dnhsYWFRY0J3ZzUvaXVGNGNHNkRRS0J5MnZSSGdLOVgxcDVKdHk2Q2JTNmNUeTNXU2NXSGlocHZlVEFCQmU3Wmd0aGtPU3FMbDNFZ1NIak4zZ1BzSk5YYUkvMFZMYkNnbUFsUythemtQR1FIZ1l1LzUxOXIyTW9vTDhLZFZoYTBPZ0s5N0wzN3VjdjJRc1FTcWFmZCtNeVFxL1lIdkU5RjBrSEp1WjZWMkdJK05lcHNsQytXeFMzRE5yUTcwdFlDQ1MxWTlaVWxVYlBiNHNKV3dvUFZpM1NJM3U2U0dwbDhGOE14UUhLSzVFcVVXcS9yN1JXU3FmeXoiLCJtYWMiOiIyODc5MWFlNjRlNTJjOTlmNjdhYTlmZjhjZWY4ZTE3NTI2ODExYWRmY2NiOTAzOTUwZGE0NTVjOWFkNTU0OWI0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InFlV0QrQTBZTm9McEJQRXZtZHNUOEE9PSIsInZhbHVlIjoiTXpIbWYzZVRYalJFSk0yNVBVMFJEOHU5dmZreFpQK1NVSTc4MEhpcmNVbzVNRVR2TlF4NkVJdjdkUkd0QXlMY01SRG1HbEVRT3FCZXdzZmF5Y0ViNEZGNmpINTVyMGNNeGJac3lqU1lPOGNIU0xjcG9yR1JCWXJiaEhRYWljWnlBeVMzQVBZNE56NzlSb2Y5RGIxclg2aFoySERIZFJ4Ymw0S3VraUpaMU9XTGFyb1NVRWJPSFZLQkdNSXM5NHczMk01Q1VmRHRjeWRkek9yWHBBVGMwb2luQjhKbVhhcXUwcEM5ZStvOXIwbmsvSi9TUGVGdTJXMVpVQ01YMVpUaHVEMHdqR2E2eU5MMENhZllJdGFWaFJpN1p2MDM5Rnpmb1hrY0NpK0tOaHBYWEZ3cmQwbUxIWGo4bXhheHVpUzhoUzd2MHcyVnp4cFRjemN5emlJNk5kN1QzVU96WHlheTNHMXdpUExiSmtMVnNCVUxuSWJ3MCt2ZWNyV3pKTDhOVGkrc0R5cklJUnBUSGsrMDBjYzB4L1RGRFhEL25xT3VUcGtOcTVUVjkrdnZGUjNSN2xJUGZGcEFheXowQjdObGFYeWhlYUZyVWZ3cHcxZzkzWllicU44ZnNZSXBhajVhWkdjc3FoZXJLRlFzSGI4OEduNUQ0Qk9nOWl2bU5yNGMiLCJtYWMiOiIyNzAzM2FhNDA1YjYyM2NhNzk3OGUyZDViYzc3NzI0YWFjOWVhMGQ1ZWZlNDEyN2JlNzk1N2IwMjQwMDg5MTRiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41708604\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1222822283 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222822283\", {\"maxDepth\":0})</script>\n"}}