{"__meta": {"id": "X3ebb19a6c04fd35e4aa5be83c7b72f9d", "datetime": "2025-06-16 09:41:52", "utime": **********.875425, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750066911.331409, "end": **********.87547, "duration": 1.5440609455108643, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1750066911.331409, "relative_start": 0, "end": **********.707478, "relative_end": **********.707478, "duration": 1.3760690689086914, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.707508, "relative_start": 1.3760991096496582, "end": **********.875475, "relative_end": 5.0067901611328125e-06, "duration": 0.1679668426513672, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45259648, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01702, "accumulated_duration_str": "17.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.807348, "duration": 0.01585, "duration_str": "15.85ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.126}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.84942, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.126, "width_percent": 6.874}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1261651348 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1261651348\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-161420574 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-161420574\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1403100479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1403100479\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-231027136 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNKaXF2NFBhMzcxVzEzVjVxMUMwQ3c9PSIsInZhbHVlIjoiQjRsUWRuSlU0Qmw5UlE4T1RnN2J1VWtoVUNHR0VrQnM2T21wOWhSL3ZiWW43K0xhR0xVVmpCTElKV3F2SVFjUXlnYVEzWEVPUzQyQVY4Rm9nZUQxT0RMZVBBRDBPKzZLT1hYT0FtNmwydmhMVVo1b2xGdGtaYW1tMXJRMVBQY05NaG9tdm16U3J6blBpdUZXWmNhdWY2eno2ZFF0ZzYyOGVWYnk0WUEwVTdzZDJqL2ZkVGtvUzY0Q0lmajIxUTdJOXNhY1ZaUGwvMDNFZUlmWHp1enBYa1EzZks2WTNPWXlMbDhzS08yUUdkOWFvS09iR1dJZ3FpeWFFOE14YlAwNFJtUFBTdnc3V01yYzFwQkxxcmIwclRNYVl6SlpnZmlvUWduTVQrWmprc3JCUDA3T3pjZUdHMEM4V085RjUvL3grVkpXZmpxUXBEZXZvZjFaaitITkdhbmlIZE1nNGU4a1JaTUVabVUrek9GYzRPL09TUHdpTzdjWTJ0cWdJTjlWeUZpbndWdWJDTGlScXF2MG5YYUg0NTU0VjR5SzhHZlJqaWNoZ2pOc1MvV0RZTG9EeUJNbjZsc0N3MktXbUdRV1FpdDNFeWNoNmNueElMaVlGZnJHMHltaEptS2JJSW1xU0M3U09Zb0xOMzBJQ0NMdkhTUjJUYUtiQXNwNlhrM3oiLCJtYWMiOiJjZTYwZWFiMDJiMjMxYjdkMmE4N2MwN2Q0ZmYwYTNkOTg0NmFiYzhhZGMwYjUyN2FmYmM3NzU2Y2E5ZjZkNzIxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJVK1haeDhCbmdMVVgvMXZIMkx5T2c9PSIsInZhbHVlIjoieEJaU3A2V1g2SXVSVlhJWndwWVBrK05hRktJbXpibUhUUzVNVXc4T3A5cU8xaC8xWWRDRTZ5V0ZuUjlSWFFGWVZvRURSaEpLWnVhLzZzMmQxbi9MbXdmRGdqSDNYWUNydjVkRENaUUhtUEphU3pibXFtb2pBNXphRkNMbDEvM3BNaFg2d2FPeTlqMllEVTRHNTBkOGV6YmlqMmE3OG9RaGtRTE14NWhlZTJhcFVEOUxuRTJ5MXJsdUtFMkNWQVpqKzZ1dTZNaUtzazF3U1ljNGdHOElVK3lpd2thTFpQNUFrZHFIWGpLckx3UG1BZ1NMMnRVQlAwbC9LVTU5eXFrQXdkNGkreXowVjk1Mm9qdERSVS9sSUhQU2h0czVsaWFwS3YxZEJhQkVUL0xvQ2J3MjRvQ09wRWUxUCtJRXN2a0FwNW82Q3J4RXJvUEVvNE9NckptOXJVQ3V4TU9LbTliSDJudzdBVlFrODVRR0VnUjNzWW5KVWNVMDZlNlNWZjlvcnRZMldzVGJBN2FodHZ3ZEVCZGYvQlA4QnNyTG9jUTlPT3J2SkhFcStOS0VZd3RwQUtzelE0dzFiL1FMVUpvVEV2YjR4Z0xNV0tOZ2xjRWF2QmE1RDlmUGY3bDVjS1o1ek83KzNDUkRRVklKY3RHOE01eEMwNVk1K0c4dXdZQmoiLCJtYWMiOiJiOWRlOGU0MTY4MzY5NjdlYjA1ZTQ1OTQyNTlkMDA5NWIwZDk0NTAwZDk0NDg5ZDczYTNiZWE2MDdiZTQ5MzY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231027136\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-195263534 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195263534\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1288689571 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:41:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlY4TFhESnBVRGIrckhIeDRLeE4zaXc9PSIsInZhbHVlIjoiWlZQcmdTdmE4QUYyWXo4eUJqa0JFbDVyUUpUbnFGODZTVi9pcTZ3RkVIbm1XaGtPbENQM1dBQ3BuZFpYKzNRUHUvMmJKdTdwaGlTazNoQUJ6QkFqbGF5RUZpT09yMDJVREtOVWNFektyVk5ZWm05T2VoT2ZsWERLTTkvRld4dXFWNjRITjd2cXdyQ3pUeW9xeEtXc2l2M0pGWGlYTXRNTU1NWnRmYVBuRWpXT3BDZHdJRlV4K3U2OVEvWHVteHhzeXNUUkNyOTU5czVDWTNXYzlWcmRRcitqSmxLc1VsSFIrbER1RFFYdDVnWWNCa1BrcUJ4VHBhclBZVVdxN1lNSUVlQkw0MVlVWDRrWU55VVN4Mm0vcGNhb2VmN0tjcGdoTTB1ZllwM1VLWjV6b0htQmY4cVJLcTFCdHBuMGFWZ0NhTkFtai9zUFExNjBqakZDR0cva2NzN3llczJzTFhBc25YR3NpVFRjQWY3Y0ErdVdKaFR4Y21pMzdzTlJLSFc1ZUxqRlF6a2xvM0JzSVVPYmRZMUlXRzBmLzViMjlRaW03eUFYdkF6MnhpdzVLMUJqdXFXTW9xUUNtaStrZFlGT3hiaXdCV1pIcTgyU25Hb1hXTnFTNi9ySHJjamo3a1BhUHMxVDF6bDMvNm0xLzlvWlR0UFMvaTRRUFhIWGo2VWIiLCJtYWMiOiJhOWM4ZWRmMGJjYjhlMzI3YmM0Y2I0MzdkMmE2N2ZhODE4MmFhZTA1ZGQxMjNmNjEzYWRkMGQ5NTdhNTBjZTQxIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imt5TEN0bU1obkpZdkJPODZyTGh3Tmc9PSIsInZhbHVlIjoianF4RHRUaUJUZWF5VWpEdDYyUG5FK25jUHFXQ0c4alV0cUQwOW5vN2IyOVVtcXpmK1hIVDN2MVlkZVZzem5OdTNOb2FjSDcydWxkckdqeFhDK3dpWGJaV3c2TXUyckpxNmlQSkFJeFoveG1heEFFc2h0aS9zOXJiNkNrMTViR2RrcXFVbGxETHQzcTdaNFhJRTBkOEZRVjJxMzUyVFFrS3NNTUdnWDJuZ2RGSy9oeUk4OXpHdjNlMFl2eDVsc1NCSmRhY1RSUmtnVmRBalBEUHVYOGVpZDJXTXR4UUp1TzJRTWs4eC9hWStIa3BjQURMY2dQRUk5Yis3ZjdFVkZRMWU4TGlwY3hMRFlNVm5TT0tHRk84bytEY0lHODc1MUJiNWduNWs1NzdrU0VCYUUrZVpweG9JUVBKUzFDcUdCbGZXTHd2Mk1BLzR0c2txblhHYm02by9aUjFLMGtNYXBLaFl1dGFEWFQ0SjZpVGpnaldhTkwrMUpiVGwvUmRheHdLT3Y0Yy9tMEJxZkNiekUrNXlORjZCV3hoSWp2azN1dDFDNWxKa1JuRTQwRUxkVXNHaklzczdNTlFiZDh6R1RSVjFLaXA5ZGpncVR1RVRxTEE2cy9wSUI0VkpUZ08yQVYzaTNyUUFUTUF6Q0RMaGNZeDRxakNiNDlIdGJ2MjBlVDMiLCJtYWMiOiJlZWZmMjI5OTg1ZDU3MzQ5MGY5YjJiYTg3YTJjOWMzN2VkNmYyZDc0NjdjMzY5ZDE2MzVkOWQ2MjNjMWY1NGYxIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlY4TFhESnBVRGIrckhIeDRLeE4zaXc9PSIsInZhbHVlIjoiWlZQcmdTdmE4QUYyWXo4eUJqa0JFbDVyUUpUbnFGODZTVi9pcTZ3RkVIbm1XaGtPbENQM1dBQ3BuZFpYKzNRUHUvMmJKdTdwaGlTazNoQUJ6QkFqbGF5RUZpT09yMDJVREtOVWNFektyVk5ZWm05T2VoT2ZsWERLTTkvRld4dXFWNjRITjd2cXdyQ3pUeW9xeEtXc2l2M0pGWGlYTXRNTU1NWnRmYVBuRWpXT3BDZHdJRlV4K3U2OVEvWHVteHhzeXNUUkNyOTU5czVDWTNXYzlWcmRRcitqSmxLc1VsSFIrbER1RFFYdDVnWWNCa1BrcUJ4VHBhclBZVVdxN1lNSUVlQkw0MVlVWDRrWU55VVN4Mm0vcGNhb2VmN0tjcGdoTTB1ZllwM1VLWjV6b0htQmY4cVJLcTFCdHBuMGFWZ0NhTkFtai9zUFExNjBqakZDR0cva2NzN3llczJzTFhBc25YR3NpVFRjQWY3Y0ErdVdKaFR4Y21pMzdzTlJLSFc1ZUxqRlF6a2xvM0JzSVVPYmRZMUlXRzBmLzViMjlRaW03eUFYdkF6MnhpdzVLMUJqdXFXTW9xUUNtaStrZFlGT3hiaXdCV1pIcTgyU25Hb1hXTnFTNi9ySHJjamo3a1BhUHMxVDF6bDMvNm0xLzlvWlR0UFMvaTRRUFhIWGo2VWIiLCJtYWMiOiJhOWM4ZWRmMGJjYjhlMzI3YmM0Y2I0MzdkMmE2N2ZhODE4MmFhZTA1ZGQxMjNmNjEzYWRkMGQ5NTdhNTBjZTQxIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imt5TEN0bU1obkpZdkJPODZyTGh3Tmc9PSIsInZhbHVlIjoianF4RHRUaUJUZWF5VWpEdDYyUG5FK25jUHFXQ0c4alV0cUQwOW5vN2IyOVVtcXpmK1hIVDN2MVlkZVZzem5OdTNOb2FjSDcydWxkckdqeFhDK3dpWGJaV3c2TXUyckpxNmlQSkFJeFoveG1heEFFc2h0aS9zOXJiNkNrMTViR2RrcXFVbGxETHQzcTdaNFhJRTBkOEZRVjJxMzUyVFFrS3NNTUdnWDJuZ2RGSy9oeUk4OXpHdjNlMFl2eDVsc1NCSmRhY1RSUmtnVmRBalBEUHVYOGVpZDJXTXR4UUp1TzJRTWs4eC9hWStIa3BjQURMY2dQRUk5Yis3ZjdFVkZRMWU4TGlwY3hMRFlNVm5TT0tHRk84bytEY0lHODc1MUJiNWduNWs1NzdrU0VCYUUrZVpweG9JUVBKUzFDcUdCbGZXTHd2Mk1BLzR0c2txblhHYm02by9aUjFLMGtNYXBLaFl1dGFEWFQ0SjZpVGpnaldhTkwrMUpiVGwvUmRheHdLT3Y0Yy9tMEJxZkNiekUrNXlORjZCV3hoSWp2azN1dDFDNWxKa1JuRTQwRUxkVXNHaklzczdNTlFiZDh6R1RSVjFLaXA5ZGpncVR1RVRxTEE2cy9wSUI0VkpUZ08yQVYzaTNyUUFUTUF6Q0RMaGNZeDRxakNiNDlIdGJ2MjBlVDMiLCJtYWMiOiJlZWZmMjI5OTg1ZDU3MzQ5MGY5YjJiYTg3YTJjOWMzN2VkNmYyZDc0NjdjMzY5ZDE2MzVkOWQ2MjNjMWY1NGYxIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288689571\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-368566355 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368566355\", {\"maxDepth\":0})</script>\n"}}