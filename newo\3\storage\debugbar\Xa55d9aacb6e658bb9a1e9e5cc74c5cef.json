{"__meta": {"id": "Xa55d9aacb6e658bb9a1e9e5cc74c5cef", "datetime": "2025-06-16 08:50:36", "utime": **********.887846, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.454883, "end": **********.887888, "duration": 1.****************, "duration_str": "1.43s", "measures": [{"label": "Booting", "start": **********.454883, "relative_start": 0, "end": **********.655607, "relative_end": **********.655607, "duration": 1.****************, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.655663, "relative_start": 1.***************, "end": **********.887892, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "232ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02122, "accumulated_duration_str": "21.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7603939, "duration": 0.01764, "duration_str": "17.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.129}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.805416, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.129, "width_percent": 7.304}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.861957, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.434, "width_percent": 9.566}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063832651%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik00V1lZSXl2cHFtbkh3a3JZbkxqaVE9PSIsInZhbHVlIjoieDhIczRDUUx4QmV0Z1o3M3paTXJjQ1BqTGxYazloZEpDalA0NE9udFVBaGtuZWUydk4wUGxSVVVRSG5zU1FxMnNjaVF6RXA3d2tLSkVMM0RvaFBnaWNBeUlib2hUc251Z0ErVHhtcEpnS0pvQzRTWExzMlJuTzg0WDFvcTRpeGlreDNZdzMxamJkWHFFbEtHV3VYNHRIRjAxOTlkTGh4bjBNUHM3cGRtRHN5NXIxQ2htZGFvTmhrajdDWjRPYnllWEkzcnhCVVR6YjlrZllmMFdMZU50cmQ4TEYrWHd6RFJBbzBxeDE4bUlqdFVTUWJQbVgyazlaSFRSTENmb2R2NFBtRmF3dE5LbTZQU3hyUVpwK1laMkQ2Q3FraTk5aWFnNUJYS0lPRElaWDl4R3hKN25uVWRZYUJ5RDZvbGpyZHFVK2d3TElSZmsvdEdIaDk2bHJ2UXZnV0Y0ZW11dWhDTTBncDBZK29EaS90emIzeTZnZWY0ZWx0ZzBJNE1BZzZScytQSjJyVzRCOENTRGVaVWY5aWJVSVVnZFVCd00rSjEzb3h3MXRJTGNxVDBubkJRWXpQOTdqa0Z2VWZZWkppUzRqYzRINkRMU3pqRlNxVi9FYVJrODlLdWkvNHdheWszeVJVemZZblZWMXNWb3pGM0MwdXJBRDdPeU1MY3NOUjIiLCJtYWMiOiI5OGM2OTZkMThmMTQ1NGZlM2U0MWE3ZjZjMGQxZWU0ZjJkMTRiNGViZmJkZDU5ZGE0ZmVjYzdhNGYzZDY5ZjQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlRRaTZ0TForaFRxZjc1RURudkJoUHc9PSIsInZhbHVlIjoiamNWZmF1YmRJNkpDZjJXYXBEaDBwa3NVcTN2Um1hUTMyTWhQZURXU3h2bllvcW5lNUVMd1FRa1lzY1ZpVzFXMmhQKzdZT0hDMjhZUjRLa1d1TWdrUTQ2eFdzQ0FBUWNENzMxQzk4Zk9OQ05hNlRJUzNYU0t5NkxUWU41V1lDeDMyNjdvOThtSkNyaWsxK3ZLeW0xT0x6ZUh1NExON1h4TmxKVldzZmpjNlV0eGJPR3dNd1VzYWQ1VkxmOSs1MzNBQkczSFBLcjdDcDNDbjl4UEJOSGoxS1NVZnBZcUgyRHd6dWVBVUJYTHFQSEVyb1RpTXlwZzZSQUhlbmRlZTNQQ2hXb2hZNVNHRDlna0lxU1BRbnZaVEdCdFEzeERpcU82dktLSlBIdzJuNUJFTjhMK0I5d2R0YWF3NzJMblY4Y0JCTEtUa05IcndrMWMrdE9Qb2hkVER3OVh1V3pCdDBqOE9pQzZpSzlvRlBzMkUzQmlqVUZOWG9XNjRObTJYUGp3cExhU2duQlo5WFJsQUVLMGk2QllMSUhvWTN3WlhZTjZnYWZudUZUWDY5MzY1NTBSdUg0dmVtMzJGamgxVG1uWGpTOERqS3BKejUxN010RnlqOXJFRm13cFo0YnVlcGtiV2FRRmx3aXFxZDNRVlNLNkJhR0Q2M3BDMXNYa3VaMmEiLCJtYWMiOiI4NjcxNDY2NzA5OTNkNjBmZjYwYWY4ZTExMjQ2YjBmNmMwMjc5NmZkMTFiOGZhNTZmZTZkZDg0NWRmNGFmYzMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1735041143 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xy8o1OLZEDaYyV6WX9XzzYzbw5zHFfm46SvuTNQ8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1735041143\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-340224619 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlLbHAzallrRG92R2pESHpUOU1DSUE9PSIsInZhbHVlIjoieGJ3MitrWVJ3YTR0VkVvMCtQandoM2tnSHo4NXVZNjJ1SnVNeHFIUWV6NEEzYURPaDdrUytzQy9IeXQ0aEFTR0xEeEZSdC9mWGdTcnZiVEhMYm1ERkxJU1J1eUUvaUJpaWt4bC9qb09HeEpMOEtydkUxZUVSVUphN1dEL1ZSMkFUazA5VWZYRmZLMko5N0pqTzN6RmdIQ1JEV0dtYWtpUi90bDZqZlJDUTduRjVuTEJJTUlGUzJjWVBFaXdjR20xYXdPdlBNZGtRb3FLc2N4YVplQ240UG50VnNYNE43djVBeHNCNVdaRkZCVmFaVUlBK09zMHFKOUE4aDVnWTZFR2xYSE1YRnpEOHBkbHFXL1IwZTdnTnJKMGVES0tZaWFZNjBhcEN2cVRwam01RmJGcFhUeW15cTQ1M3FsTjRQajdybHAyUGI2M1dHZGlYVjBOSzY2MGRmZ01nY3UwSFdjcUxjd21NamtFNm16dkpKRjJrbGtSUmg5ZjQ4dXZnbG5lcWtEalFWVGNWR3REbDJVcElZVzQ1dHJ1a0J2OWZlc2V2VjlNT3R3T2xTLy9iblZqOUpPUUhkTXJjZ3VWblBSWWlwSEN5ekhvMlVHTWZZbHhzZHZzNDBneFJaaTc4SzhsT1grMEZiTThUVTNsQUp2NjUvV0tVRS9YT1ROc29QQlQiLCJtYWMiOiJlMjBhMjQ2MmZmYTQ1MjBmMDcyOGNmNDc0OTBlNjZlYzIxYWQwMmJjNzQ5YTEyYmVkMzEwZDM4NTJlYzVkNTU2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZCNU5ZeXdNRVVEZzU4TGRsOHlBNGc9PSIsInZhbHVlIjoiM2pRK2JhZHRWdlc3S1BaYk5tQTdqait6ZFJjSlVDZ0VjdXVrQm1QcC9rcDJYTWpVUnBqeVF5ZE8yNURNV2hFLy9VZmpqT1F4Z2V3WmxkWG1Ob0JOZVd3cUh1aXh2Z3ZXQTk5NGpFQXVReFdxRGY0YnlweXN1NHdKa08yc1NQR3lVOVZJVjc3L2xCRU9oNmwxazJhcUQrL2pyTkpwU2NhWDZ2RTBJVzNSNlluR1J3cHg4NTBuSFpXRGVycHAxNnJmYkJSallqcUtTNjZobXlaSUxqallNTWEramxXSjNTbHk3c2hJTS9NVmRSb01KOWVOOU9JZThIZWJUV2x2eEVoeFR0Ui9rYUFtbnpFckNzdkNKdW1mVG0wT0RNb1JBcmFrZzdBa2tDcG0zNVQxMFVoNEYzdVJaaHRoeW10T0RESEhadUFCYytaNU9HRzN6U1BCdVJPVDZSc0s4YjFDSlFrcldLbFd6b2RFanpPWkgwVHpxNi9BUkxXbEk5OHcrdjI5RkQ5cllOYnQxSXJ6NFkxOTZDR05lQzhPV3hrMTlzak0rY2FmdjVlQnk0ZW8wU1FJRjk3SStIL1VWNWJ3MFVUc1FHWDBYSzlTYVhyd3Fac0FKaDVUTzQ2SlJqNHFlcWt5cGNjZGJ5NEN3a2ZPVEZxaHR4QzdLMVVqNDQ3Q0JQMmoiLCJtYWMiOiIzNDgxNDI5Yzg0NTQzMDZjZDQ4YzkzMGI0MjdjNTE0OWRjYzEyZjljZTMyMGY4NTYzZjI5ZDdlMTVjMWMxMzZiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlLbHAzallrRG92R2pESHpUOU1DSUE9PSIsInZhbHVlIjoieGJ3MitrWVJ3YTR0VkVvMCtQandoM2tnSHo4NXVZNjJ1SnVNeHFIUWV6NEEzYURPaDdrUytzQy9IeXQ0aEFTR0xEeEZSdC9mWGdTcnZiVEhMYm1ERkxJU1J1eUUvaUJpaWt4bC9qb09HeEpMOEtydkUxZUVSVUphN1dEL1ZSMkFUazA5VWZYRmZLMko5N0pqTzN6RmdIQ1JEV0dtYWtpUi90bDZqZlJDUTduRjVuTEJJTUlGUzJjWVBFaXdjR20xYXdPdlBNZGtRb3FLc2N4YVplQ240UG50VnNYNE43djVBeHNCNVdaRkZCVmFaVUlBK09zMHFKOUE4aDVnWTZFR2xYSE1YRnpEOHBkbHFXL1IwZTdnTnJKMGVES0tZaWFZNjBhcEN2cVRwam01RmJGcFhUeW15cTQ1M3FsTjRQajdybHAyUGI2M1dHZGlYVjBOSzY2MGRmZ01nY3UwSFdjcUxjd21NamtFNm16dkpKRjJrbGtSUmg5ZjQ4dXZnbG5lcWtEalFWVGNWR3REbDJVcElZVzQ1dHJ1a0J2OWZlc2V2VjlNT3R3T2xTLy9iblZqOUpPUUhkTXJjZ3VWblBSWWlwSEN5ekhvMlVHTWZZbHhzZHZzNDBneFJaaTc4SzhsT1grMEZiTThUVTNsQUp2NjUvV0tVRS9YT1ROc29QQlQiLCJtYWMiOiJlMjBhMjQ2MmZmYTQ1MjBmMDcyOGNmNDc0OTBlNjZlYzIxYWQwMmJjNzQ5YTEyYmVkMzEwZDM4NTJlYzVkNTU2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZCNU5ZeXdNRVVEZzU4TGRsOHlBNGc9PSIsInZhbHVlIjoiM2pRK2JhZHRWdlc3S1BaYk5tQTdqait6ZFJjSlVDZ0VjdXVrQm1QcC9rcDJYTWpVUnBqeVF5ZE8yNURNV2hFLy9VZmpqT1F4Z2V3WmxkWG1Ob0JOZVd3cUh1aXh2Z3ZXQTk5NGpFQXVReFdxRGY0YnlweXN1NHdKa08yc1NQR3lVOVZJVjc3L2xCRU9oNmwxazJhcUQrL2pyTkpwU2NhWDZ2RTBJVzNSNlluR1J3cHg4NTBuSFpXRGVycHAxNnJmYkJSallqcUtTNjZobXlaSUxqallNTWEramxXSjNTbHk3c2hJTS9NVmRSb01KOWVOOU9JZThIZWJUV2x2eEVoeFR0Ui9rYUFtbnpFckNzdkNKdW1mVG0wT0RNb1JBcmFrZzdBa2tDcG0zNVQxMFVoNEYzdVJaaHRoeW10T0RESEhadUFCYytaNU9HRzN6U1BCdVJPVDZSc0s4YjFDSlFrcldLbFd6b2RFanpPWkgwVHpxNi9BUkxXbEk5OHcrdjI5RkQ5cllOYnQxSXJ6NFkxOTZDR05lQzhPV3hrMTlzak0rY2FmdjVlQnk0ZW8wU1FJRjk3SStIL1VWNWJ3MFVUc1FHWDBYSzlTYVhyd3Fac0FKaDVUTzQ2SlJqNHFlcWt5cGNjZGJ5NEN3a2ZPVEZxaHR4QzdLMVVqNDQ3Q0JQMmoiLCJtYWMiOiIzNDgxNDI5Yzg0NTQzMDZjZDQ4YzkzMGI0MjdjNTE0OWRjYzEyZjljZTMyMGY4NTYzZjI5ZDdlMTVjMWMxMzZiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340224619\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-921924741 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921924741\", {\"maxDepth\":0})</script>\n"}}