{"__meta": {"id": "X4630171c2bcd101ed527895fd6227bdc", "datetime": "2025-06-16 08:50:16", "utime": **********.719871, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063815.037763, "end": **********.719913, "duration": 1.682149887084961, "duration_str": "1.68s", "measures": [{"label": "Booting", "start": 1750063815.037763, "relative_start": 0, "end": **********.357616, "relative_end": **********.357616, "duration": 1.3198528289794922, "duration_str": "1.32s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.357646, "relative_start": 1.319882869720459, "end": **********.719919, "relative_end": 5.9604644775390625e-06, "duration": 0.3622729778289795, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46087152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.565695, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.593945, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.677997, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.692904, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.04313999999999999, "accumulated_duration_str": "43.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4602702, "duration": 0.0189, "duration_str": "18.9ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 43.811}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.489504, "duration": 0.010490000000000001, "duration_str": "10.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 43.811, "width_percent": 24.316}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.514803, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 68.127, "width_percent": 3.616}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.568696, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 71.743, "width_percent": 3.408}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.59734, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 75.151, "width_percent": 3.245}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6400619, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 78.396, "width_percent": 5.818}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6543279, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 84.214, "width_percent": 3.732}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.66154, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 87.946, "width_percent": 3.616}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.6821811, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 91.562, "width_percent": 8.438}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GdAP3Ala05cLcMv8CjWhgdpBs1UQ8quTxt10NpT1", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-871530961 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-871530961\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1830437297 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1830437297\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1948180841 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1948180841\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1071406969 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IjU5RFJIZUtiWmlCRms4NXBGb1FyOGc9PSIsInZhbHVlIjoid0Nuc3h0bEltRGZRV0p5NkdWYWRqOEl6ZG55QVVSL0JhNXRTRWkzUUVuTk5OcWNMNHRmbHZQZHJQUlNDemdtRzZwTTYwbForQk8rVVcycmZGbHdtK1M1eGNGL0trMHAyaHA0bEZaQ0FpN3dMaFRCcUF3NlhIUzZZSGRIa1lmcnlKN3NtLzhQWGtGODIwMjJuQXRMMjhkdzVnTnhubFkvTmJNQS9qTUpJM0VIZzVUaFVwampqaVRmOFpuOTgrL21wLzRXbjd2d2NvQlRMUjJNdlkzTWxaVGU5Ti9GTVhzMk5CNlJiU0E2MlNuMmk3Q1d6MTdNanRablJrMHVsR2VKZXhCelUrTGtFVFVoamw5UGVLN2o4VWNlWkZnOVRzUTBCSTVqbFlMZmwrT2xYbG9zTTlEanhEL2pWLzdveGEyQytObThzRHZCQzhhSHB5dnR1Z3VON25VL21yZ21jZkJkeW9RSDRLN0tySmpWTEV5TFE4Tm9JVllSQ25ucVhuejA0b2xLYnJ1SC9yN1cxM0NBR0x1NS8relMxV2tzZnZmLzVoL0pSd1R1TjdaS0Q5Z1pqajNia3lRRjFEWW9kbGIwWGxGR3B0L2NKaEowYlBLTHluMWt5d2pXbUo3VUI0dkNJNWZpellhNEx0aVJURnhMTDhFQXY1SU1saTR4QUQwdS8iLCJtYWMiOiJkMjkzNDRkNjUxODQyYTBiZjY4MDhmODFjOWU1M2E3NGFkZTZjMDc3NDg1OTcxMGExMWRmNzhlNTlmNDJjNGY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdsbnlOZitFOFdPZENkUEp3OXc2YXc9PSIsInZhbHVlIjoiOHVwMitlY3NmTnF4U3JuRjZKQUNHZU5uWmdpUWVGckR4QVNicUdYSlpHRXpxMk9hNnNhbVpEWnV2ejdCNmdrU1ljb0pCeDRsVHFkdmZobXh0Mk9FdXpycVJPSVljT3Q5cnROQ04wNFR2YWp0YytNTWs2N3JLOHB4Z29GT1VHQ0d5UVpnWkZYTTFWSEhaRnVtYTV4Mm1FMklBdFdsOGN6aVZYVWgva1g5Rmh3blhYcTJLbnlKVy9ITUxpZFZOTjRLRmZOL3JoSkVxLzFNTVE5cVNxZjN3RHF3N2pIVlIvYlNLK1daTmlUT3A0T1FsZGZMVisvMTZUQURIZTQ3SUE2K015ZFRIM2wrenMvRFlTN3UyMW5xbkxhc3pnZlI0ajM0MGhYMXZ5cVdQTTZXbUpvcUNoZ0ptMjU3ajcvVjd0WUJKa2ZwVlQxZEs3azNsT3Z4OHpKTllhaFpKUDA0bzNublRha2dBbTlBcitWMjluREJYNG5KTWNnelJ6c3NmNGtORmtNYXBKcU42YlhZa3VDTEJ0NFJqRlNKQVlEVFU1bUhuUXNWQjVnR2pYS2RmeS96SzNwWXVXcmJCRWhJNCt1SWkvWCttZDFLU045SVhZeHhIMVN3cnBNQnhMZjJubGxheXVtVXZicmF2ZzZtY3IvWGt3Y3NieEpLQ3BMZlEwRVQiLCJtYWMiOiI1OWVkYjY1MWQyZjQ2YjE3MTA5ZDg4NjU4MTU5YzM3YmI3Y2I1NzVkMmExMWQyMTVkNzI3ODUyODY4ZmIyZTFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1071406969\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1031907939 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GdAP3Ala05cLcMv8CjWhgdpBs1UQ8quTxt10NpT1</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">O5XDuQ3r4UrGEpFQ3izOSvW7CQSr0tGa2huyUcWh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031907939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikt3ZDdPWXF0WGpNdDNyNjAySm4rTXc9PSIsInZhbHVlIjoiTjJzejk3UjhlK3AyVnlCV0F0OWl2NFlrWnprNHR5SHd4VlJoSTJMK016dDI2M05FVWFMYjhHSDc1ZFp6YmdqRGZTcENLZ04wR2t3a2lPYXQwU2ZWUXdtSitVUVBVWlpRd2VyNGhkRS9XMHZCdU5wMFJ0N04yTWdUakw2VG5OSzV2ZVVWMFluVVhPKzQrL1J1R1VwWWZDQWg3T1QvY2V5NmR2Vm1jRUJYNW1wcW1iVDBYbU1ObnY1WFJnMXVoUWhSU2JXaVhYSVBqOUdkd3d5c3dLMTU5WVpNbVQ2MXVOeFFnZ0xtRFo0d2hMN3JsWncyMW1KL0l1eEhmaysxb2VXRVlGbjRUc1hnUGhSOFZveTBXUVZqRGh2ckNwNUtlVVZrUVlaMy9lRnFjckZsbjI2Uno1aURrZFhTb0hhWWY1TTU0bUVER2g0czRLc1lFTTg3ek4xUHYvNnhITjlqYjRrcFNLV2tlNldrbWFDa3FVV0J2Z2lMZVZtZklMMEFoRjhVWS9tSFJ2Q2VxTHM3TGZVQnozYzhLb3lDcldBK0F6bU9udmZuRnlRUk15VWlkcXVsYzJjaFJmaWZLbHR4QVpYSnBGZFZPOXVFYk9nZ3FOYUE0d3EzNHpnQXdhWUUwK1M3YzM0MzR2VmZpSGE2QkJuY21QZ0Rxbk5aeGYyZVdqdEUiLCJtYWMiOiJjOTcyOWRmMzQ5NTRlMmVlOGJjYmM0MmVlOTY2ZDYwODIxZWUyNmYyNmMzYWM0Yjc5ZmM0MDBiY2NkZmIzZjg0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9pM2xRTThVTG9hNGJaWEl3LzVmYnc9PSIsInZhbHVlIjoiKzdNR1VOS3ZDNitxSzNrNmxnT0RLbE1FRHJMUnI4TE9NZ3FXd1EwY1F5TFJGSG0wTkVCbUVyMzFGaW4ySjNHOG0rRVFvWHd5eWhuQllsRGl0eE9SZzJUTTU5SEtLWSsrWkwwNUJmM2htS3YvYWl3all3dzlZdWYrVDRaa1ZDaXBZLzByZlJPN3ZqOWh3S1h2LzRFRkRiWS9lTkVaVDZ4ZUk1SnljQ3VTMTlzajVkTGljREsza21zZmI2NTJPMmtNZXhpOTV0dmRqWmIyZ2xnRGF4SENiNFRvcWc5K0svczdHTjd5Y3FEZUZsbld6M1lOdVI5Q0J1ZkxIRlNnSHVSV09KKzFNbTlXakY4MnpTbHJNa2pKU1JjV1F5SnNBeU4yUFlNemlJdnAzemVFNXA3dFp1ZlFuaTJvL0wxSEsvLzR4ZGVxbWR1Y3duMy91djJmcWJnWFJsVEZuQ1VQRHBFazc2ZDZrT0t3aFFsWVB3VUNqaUtZcjFCMU9lemJaZ2E3YWQrck1OM1J5MFdFYVQ1SDRKeUtaY1FwZmpGdU96Z3hINXNIODdTQ1pzdHc3ZEVYSWpWOTUrQkdONFkvcDRFNFcyOFpRNTQwaGZpY0VPbE9KOGxGZ3IxUXNGVC8vek9KaEZlTVNDcGJraWNxNFE4NUpnYVBMU0V1QkpISTFpa1oiLCJtYWMiOiI3ZWZmNDBiMzUwZWY2YzQ3NWNlOWUyYjAyMjAzNTdkMGRmMDA2N2E0NzI5NDYzZmNhNjdiMDQ3NThmZjFhYzAyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikt3ZDdPWXF0WGpNdDNyNjAySm4rTXc9PSIsInZhbHVlIjoiTjJzejk3UjhlK3AyVnlCV0F0OWl2NFlrWnprNHR5SHd4VlJoSTJMK016dDI2M05FVWFMYjhHSDc1ZFp6YmdqRGZTcENLZ04wR2t3a2lPYXQwU2ZWUXdtSitVUVBVWlpRd2VyNGhkRS9XMHZCdU5wMFJ0N04yTWdUakw2VG5OSzV2ZVVWMFluVVhPKzQrL1J1R1VwWWZDQWg3T1QvY2V5NmR2Vm1jRUJYNW1wcW1iVDBYbU1ObnY1WFJnMXVoUWhSU2JXaVhYSVBqOUdkd3d5c3dLMTU5WVpNbVQ2MXVOeFFnZ0xtRFo0d2hMN3JsWncyMW1KL0l1eEhmaysxb2VXRVlGbjRUc1hnUGhSOFZveTBXUVZqRGh2ckNwNUtlVVZrUVlaMy9lRnFjckZsbjI2Uno1aURrZFhTb0hhWWY1TTU0bUVER2g0czRLc1lFTTg3ek4xUHYvNnhITjlqYjRrcFNLV2tlNldrbWFDa3FVV0J2Z2lMZVZtZklMMEFoRjhVWS9tSFJ2Q2VxTHM3TGZVQnozYzhLb3lDcldBK0F6bU9udmZuRnlRUk15VWlkcXVsYzJjaFJmaWZLbHR4QVpYSnBGZFZPOXVFYk9nZ3FOYUE0d3EzNHpnQXdhWUUwK1M3YzM0MzR2VmZpSGE2QkJuY21QZ0Rxbk5aeGYyZVdqdEUiLCJtYWMiOiJjOTcyOWRmMzQ5NTRlMmVlOGJjYmM0MmVlOTY2ZDYwODIxZWUyNmYyNmMzYWM0Yjc5ZmM0MDBiY2NkZmIzZjg0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9pM2xRTThVTG9hNGJaWEl3LzVmYnc9PSIsInZhbHVlIjoiKzdNR1VOS3ZDNitxSzNrNmxnT0RLbE1FRHJMUnI4TE9NZ3FXd1EwY1F5TFJGSG0wTkVCbUVyMzFGaW4ySjNHOG0rRVFvWHd5eWhuQllsRGl0eE9SZzJUTTU5SEtLWSsrWkwwNUJmM2htS3YvYWl3all3dzlZdWYrVDRaa1ZDaXBZLzByZlJPN3ZqOWh3S1h2LzRFRkRiWS9lTkVaVDZ4ZUk1SnljQ3VTMTlzajVkTGljREsza21zZmI2NTJPMmtNZXhpOTV0dmRqWmIyZ2xnRGF4SENiNFRvcWc5K0svczdHTjd5Y3FEZUZsbld6M1lOdVI5Q0J1ZkxIRlNnSHVSV09KKzFNbTlXakY4MnpTbHJNa2pKU1JjV1F5SnNBeU4yUFlNemlJdnAzemVFNXA3dFp1ZlFuaTJvL0wxSEsvLzR4ZGVxbWR1Y3duMy91djJmcWJnWFJsVEZuQ1VQRHBFazc2ZDZrT0t3aFFsWVB3VUNqaUtZcjFCMU9lemJaZ2E3YWQrck1OM1J5MFdFYVQ1SDRKeUtaY1FwZmpGdU96Z3hINXNIODdTQ1pzdHc3ZEVYSWpWOTUrQkdONFkvcDRFNFcyOFpRNTQwaGZpY0VPbE9KOGxGZ3IxUXNGVC8vek9KaEZlTVNDcGJraWNxNFE4NUpnYVBMU0V1QkpISTFpa1oiLCJtYWMiOiI3ZWZmNDBiMzUwZWY2YzQ3NWNlOWUyYjAyMjAzNTdkMGRmMDA2N2E0NzI5NDYzZmNhNjdiMDQ3NThmZjFhYzAyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GdAP3Ala05cLcMv8CjWhgdpBs1UQ8quTxt10NpT1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}