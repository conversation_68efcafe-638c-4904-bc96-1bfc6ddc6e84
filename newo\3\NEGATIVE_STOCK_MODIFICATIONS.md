# تعديلات نظام POS للسماح بالبيع على الكميات السالبة

## نظرة عامة
تم تعديل نظام POS ليقبل البيع على المنتجات ذات الكميات الصفرية والسالبة، مما يسمح بتسجيل المبيعات حتى عندما لا يتوفر مخزون كافي.

## الملفات المعدلة

### 1. Controllers
- `app/Http/Controllers/ProductServiceController.php`
  - إزالة فحص المخزون في `searchProducts()`
  - إزالة فحص المخزون في `addToCart()`
  - إزالة فحص المخزون في `updateCart()`
  - تحسين عرض الكميات بألوان مختلفة

- `app/Http/Controllers/Api/PosApiController.php`
  - إزالة فحص المخزون في API

- `app/Http/Controllers/NegativeStockSettingsController.php` (جديد)
  - إدارة إعدادات الكميات السالبة

### 2. Models
- `app/Models/Utility.php`
  - تعديل `warehouse_quantity()` للسماح بالكميات السالبة
  - إضافة `allowNegativeStock()` للتحقق من الإعدادات

- `app/Models/Quotation.php`
  - إزالة فحص المخزون

### 3. Views
- `resources/views/quotation/quotation_create.blade.php`
  - إزالة التحقق من المخزون في JavaScript

- `resources/views/quotation/edit.blade.php`
  - إزالة التحقق من المخزون في JavaScript

- `resources/views/settings/negative_stock_settings.blade.php` (جديد)
  - صفحة إعدادات الكميات السالبة

### 4. Database
- `database/migrations/2024_01_01_000000_add_allow_negative_stock_setting.php` (جديد)
  - إضافة إعداد `allow_negative_stock`

### 5. Routes
- `routes/web.php`
  - إضافة routes لإعدادات الكميات السالبة

## الميزات الجديدة

### 1. عرض المنتجات في POS
- **الكمية الموجبة**: badge أخضر
- **الكمية صفر**: badge أصفر (تحذير)
- **الكمية السالبة**: badge أحمر

### 2. إعدادات النظام
- صفحة إعدادات مخصصة للتحكم في السماح بالكميات السالبة
- يمكن الوصول إليها من: `/negative-stock-settings`

### 3. تسجيل المبيعات
- يمكن بيع المنتجات حتى لو كانت الكمية صفر
- يتم تسجيل الكميات السالبة في المستودع
- تتبع دقيق للمخزون السالب

## كيفية الاستخدام

### 1. تفعيل الميزة
1. اذهب إلى `/negative-stock-settings`
2. فعل خيار "السماح بالبيع على الكميات الصفرية والسالبة"
3. احفظ الإعدادات

### 2. البيع في POS
1. اختر المستودع
2. ستظهر جميع المنتجات حتى لو كانت كمياتها صفر
3. يمكن إضافة المنتجات للسلة بغض النظر عن الكمية
4. عند إتمام البيع، ستصبح الكمية سالبة في المستودع

### 3. مراقبة المخزون السالب
- راجع تقارير المخزون بانتظام
- تابع المنتجات ذات الكميات السالبة
- قم بتجديد المخزون عند الحاجة

## ملاحظات مهمة

### 1. الأمان
- تأكد من مراقبة المخزون السالب بانتظام
- ضع حدود للكميات السالبة المسموحة
- راجع المبيعات والمخزون يومياً

### 2. التقارير
- ستظهر الكميات السالبة في تقارير المخزون
- يمكن تتبع المنتجات التي تحتاج تجديد مخزون
- تقارير المبيعات ستعكس البيانات الصحيحة

### 3. النسخ الاحتياطي
- قم بعمل نسخة احتياطية قبل تطبيق التعديلات
- اختبر النظام في بيئة تجريبية أولاً

## استكشاف الأخطاء

### 1. المنتجات لا تظهر في POS
- تأكد من تفعيل إعداد "allow_negative_stock"
- تحقق من وجود المنتجات في المستودع المختار

### 2. لا يمكن إتمام البيع
- تأكد من صحة بيانات العميل والمستودع
- تحقق من صلاحيات المستخدم

### 3. الكميات لا تتحدث بشكل صحيح
- تحقق من دالة `warehouse_quantity()` في Utility.php
- راجع logs النظام للأخطاء

## الدعم
للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة logs النظام أو التواصل مع فريق التطوير.
