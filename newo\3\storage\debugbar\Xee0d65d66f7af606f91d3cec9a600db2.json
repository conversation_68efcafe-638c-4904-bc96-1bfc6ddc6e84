{"__meta": {"id": "Xee0d65d66f7af606f91d3cec9a600db2", "datetime": "2025-06-16 09:53:36", "utime": **********.69233, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067615.122394, "end": **********.692363, "duration": 1.5699689388275146, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1750067615.122394, "relative_start": 0, "end": **********.502066, "relative_end": **********.502066, "duration": 1.3796718120574951, "duration_str": "1.38s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.502091, "relative_start": 1.3796968460083008, "end": **********.692367, "relative_end": 4.0531158447265625e-06, "duration": 0.1902761459350586, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45260208, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02316, "accumulated_duration_str": "23.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.6111188, "duration": 0.021670000000000002, "duration_str": "21.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.566}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.664975, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.566, "width_percent": 6.434}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1444024994 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1444024994\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1002145121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1002145121\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-165301460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-165301460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IlVLazdZMTNrd3AvVXVvTzVnYldyTXc9PSIsInZhbHVlIjoiTWZsZnBRM0M5L1lZMXN5OHFDcXVKdVRrY0tyR2loc0dVVEFRUDhvU3R2T2JWSTFydW1lTEI3SnYxdlkvUnFlMzR1d3dnZG9GTWl2aStmNlpPY2tLZyt4anU4MUlEL3dZYi91WGkwNEFoSUxOU2ZJR1hJNXJFZVkwRWRyazJtN1ROVzM3Q2VmazIxb0JLQjB5QnJhVWx0MlVvV1E1T1RHUkk0aHY2T0x5TmtVam9xVTFuT28yY2hLM2VneTU4bFdIakZtVkE0cXNxVzhXczhlTkdCUnNDTGxqdFJXSE9rMWQzYnBJczdlSHpwcUxaSlVLM1Q1VzM5QVB0SXZlNE9ZcmRpZisxSHQ3NVgyZXd5SkY2Z1dZTDQ1TW5JbkdmUG4xczBzeEdLNlJrblZUMmVod3lnUUVKak9Jb0FsdzdDV04zOHZoTVNCM2RncmNGVWlVSkxHUjF4SXZTcmJRU05zY1dvQVYwYmQ1V3V1V3RjZytrdkF6M0o0N0lYMU1rdkxycG1xb2VpOGo2SERKeko0SGxtMkRQWWR2MkdXUitEK1dVcUsxRS9oMlEwWDkrQVVoVnF0alBQV04xZW8rTmY5ZGJJRDQ5ZG9XQTBrL1FBN2t5YWlnSm1pUTN0WW1tRlNqT0tqVUFlbzZnYk93cnZaMlFjUzhhS0JnUmRJZDdlZGciLCJtYWMiOiI5YzUzNGMwNTk5MjlmMzllODc1OTNhOGFmODI4Y2VlMDRkMGZiN2U3NjA4MjJkYjNiOWI0NjEwNjAwNmZlOTBkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRjM09pN0ZXcm56T2FWZnhoTC9HUXc9PSIsInZhbHVlIjoicTU1VEJtSzZsS1lBdXl0ZTZsWGV3NXhSZ0xwWHBBNnExN0pNZWlSbGNtcVV2SzQ3MXBDNjk4aVZPUkU4alBlUFJsU2JOQUdFb3I0aGpXTEJ6ZHV3M3YyWjlZUmZXeW51QUNsL0xoemRhRUl1TzF3Nml0UzQybnUxbFZES3YvY3BzYTArZzZYWGJDNlBvTWkzaDJOTGxCNG1KcXh1QjNmajRVUGJPYUJzM0Z0VXFjc2xQK2tGY25raW92Y1FhWEtrV0t6SlNldUVzYnJQdm96REJ3eWQ3eHpweExFV05pbkhyb3hackZmaXpPZWRuZElaRWxkaGZkckV1R1NnbGx0bDN5UzlFWjR5RDJ0RXdqbFp6K2NvZ2ErL3R2bXlvRGtmeCtxWG16NWRRUzREaXBCeTFLWS93RWVad0p0N1dUaFhyYlA3RmJUK0J4Tjd3WXFKSlF1NUhJcGx6TzZmQVBFU3NhMWdnS1lzVFplYkxTeU5yTjFDemtNZWZkc2UxMk1nUUhnWUdzTkFMZDZsVm5iam91VWV2dlV4WEttY2hYSlFWQUtXZnJ0NUFNc3haRXp4Wk1QcXdkei94WWI0YmF5TjdhOHdvWjEvUE5XSFRtaXBwSk1PRjJrK3ZkQjRUZmpvR0JjY3BaaDdQbjBZT3RrWGlFUDhvdndMVktxeWhTMlkiLCJtYWMiOiIxYTY3OWUzMWQ4MTU2OGJhZTcxMzQ3ZDZlMWY0M2FjYTU5OTUwYWEwODRmYzIyNWQ1ZmZmMzBmM2Y5MjA1ODFmIiwidGFnIjoiIn0%3D; _clsk=116a2vl%7C1750067612209%7C4%7C1%7Cq.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1036923324 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036923324\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2085767127 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:53:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVUQmNtOThqZEpyeTlCOTlsZ1pUdnc9PSIsInZhbHVlIjoiRlBpeitjU05xL1lNYlY2bGVISWNhSkZsUGwxR0VOaDdOck1IZ0Fkb3pVdTl5U3ZvTEJRbkcvNGtFZ2tnTXVndDRTNitJNDVOY2lIQXp0bXBMRk56ZWw0TGNwZGptQ1NYOXRqbFBqR1V6ZXBucFpXMlJyT0ZiZ0szWCtNSG4vUzhva1dBRFpCUmZ1ZnIxOE1pakx0NkNGdStzWDR5dUdEMnlQZGRmK2dnT1kxZUc4QVpLRkJJTFViWjY4bjNKSE5EaDVtMWtxSDNidHI5Qk5Ha3BvQnhZOEo5TXRtZVE1OE5sdzc3bGZxSm96T3h2dVNFdjZ6YjlIelBPd215U3drZlJ2QnhjcHIrYmM0VitmOHhGL0FzUDZFZU52RnNIc1hkTFlMdFd5NHZTUjJUZENITC9ianAwMTNKZjgvL200Mk4zSWV1TS9ncW04aHJyanpEbjZRTkxBSkRDQVRSQzIxOTFBU2JLRWJ2MFd4azk2cUlhU2UwSkpSdDNlMEpUK1owbGVvdGpqYklCbUZPcXdlczNlWXU5cFBCV3FhM3Rxb1FudERucVQwZS9jTlJaZ2dNNkJHUkExR2xHMzBSVm15MmdmMGpKTytacDBtUnlxdWpTWkFtL2tkekZUa3hVUFZkYVZpY3htMTQ1SFVxK3h1Z2U4Sm92VWMyTUh0YXJtMHQiLCJtYWMiOiJkNjYwYmI0OWUzZjY4OTllMWQ5NTJhMDlmZmFmMGFiNTI4MGRiYmZiNWExYzkwYjgwYjI2YmI1ZGUxMTAwMjhjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlhajJVSXc4U25qM1RSMGQ3dUFHclE9PSIsInZhbHVlIjoiLzJKQVdTWHN5ZkE5MjVMbWR4bGVocVpTV0dmV1A3cTRkejB6cml6Rm10RHlCS0lXY1d3TnQrRWtBcEZEQWQrNFRDckoxdDFwTVdGZEV0b25sOHRsY01zRzNvdVBzSHRYdE13WWNLN3hJRUtNUStZT2E2SkRScGg3cVZILzRiYzFZTVh1SVF3U1ZxMGFiZmJmL1NZUW91dnpacWh0ejc2VTlvZ3h2L0dvVFF5YUdIdkgxUllhWXZPbVZkRWRURXErSERrdWw5dUk4YzU1YjdtVllQdFJrK251RStsS20rVnJJRFlYQW5IRDlnMzBoNHU3YUt3SUpYV1FFbzh1R1RLbTdKME0wTVIzTnZrTXBZczJtZFpnVlRCS2dUSEsrekhyaGtDRWVFK2ZTd1ZqbnFMNHF4VHc0b25jbEx4WnhFaTJPY014N1BxQk5zWjlJVDlMOHZCVllPTFNQNGtuek4xZnJHc0R5OHpOUGg0MFg3VnB2Y2NSWVhXWFM5a3JISnNtZ0ZUNmxuTVlFNEFKQjJVd004anM3QVR1bG5JOHRCNGdacDRocDBkUDNFSE5Rb0lNL2pzV3ZPc1dMWEtrYWhFUXNzcFQxNTIwZnJiaU4wcHVIbWFOZWZJVTdsRE11dHUzM1BteG14bXBidVlTb25aSDJzN1QxUVlpcTU0SlEzWU4iLCJtYWMiOiI0MjVjNTJhYjJiMzM2M2IzY2YwYjc3YmFiYzk1OTc1OTFiMjBlZjk2ODllYTNiOTU0ZGM0MzUyYzVkNWEzNjY1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVUQmNtOThqZEpyeTlCOTlsZ1pUdnc9PSIsInZhbHVlIjoiRlBpeitjU05xL1lNYlY2bGVISWNhSkZsUGwxR0VOaDdOck1IZ0Fkb3pVdTl5U3ZvTEJRbkcvNGtFZ2tnTXVndDRTNitJNDVOY2lIQXp0bXBMRk56ZWw0TGNwZGptQ1NYOXRqbFBqR1V6ZXBucFpXMlJyT0ZiZ0szWCtNSG4vUzhva1dBRFpCUmZ1ZnIxOE1pakx0NkNGdStzWDR5dUdEMnlQZGRmK2dnT1kxZUc4QVpLRkJJTFViWjY4bjNKSE5EaDVtMWtxSDNidHI5Qk5Ha3BvQnhZOEo5TXRtZVE1OE5sdzc3bGZxSm96T3h2dVNFdjZ6YjlIelBPd215U3drZlJ2QnhjcHIrYmM0VitmOHhGL0FzUDZFZU52RnNIc1hkTFlMdFd5NHZTUjJUZENITC9ianAwMTNKZjgvL200Mk4zSWV1TS9ncW04aHJyanpEbjZRTkxBSkRDQVRSQzIxOTFBU2JLRWJ2MFd4azk2cUlhU2UwSkpSdDNlMEpUK1owbGVvdGpqYklCbUZPcXdlczNlWXU5cFBCV3FhM3Rxb1FudERucVQwZS9jTlJaZ2dNNkJHUkExR2xHMzBSVm15MmdmMGpKTytacDBtUnlxdWpTWkFtL2tkekZUa3hVUFZkYVZpY3htMTQ1SFVxK3h1Z2U4Sm92VWMyTUh0YXJtMHQiLCJtYWMiOiJkNjYwYmI0OWUzZjY4OTllMWQ5NTJhMDlmZmFmMGFiNTI4MGRiYmZiNWExYzkwYjgwYjI2YmI1ZGUxMTAwMjhjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlhajJVSXc4U25qM1RSMGQ3dUFHclE9PSIsInZhbHVlIjoiLzJKQVdTWHN5ZkE5MjVMbWR4bGVocVpTV0dmV1A3cTRkejB6cml6Rm10RHlCS0lXY1d3TnQrRWtBcEZEQWQrNFRDckoxdDFwTVdGZEV0b25sOHRsY01zRzNvdVBzSHRYdE13WWNLN3hJRUtNUStZT2E2SkRScGg3cVZILzRiYzFZTVh1SVF3U1ZxMGFiZmJmL1NZUW91dnpacWh0ejc2VTlvZ3h2L0dvVFF5YUdIdkgxUllhWXZPbVZkRWRURXErSERrdWw5dUk4YzU1YjdtVllQdFJrK251RStsS20rVnJJRFlYQW5IRDlnMzBoNHU3YUt3SUpYV1FFbzh1R1RLbTdKME0wTVIzTnZrTXBZczJtZFpnVlRCS2dUSEsrekhyaGtDRWVFK2ZTd1ZqbnFMNHF4VHc0b25jbEx4WnhFaTJPY014N1BxQk5zWjlJVDlMOHZCVllPTFNQNGtuek4xZnJHc0R5OHpOUGg0MFg3VnB2Y2NSWVhXWFM5a3JISnNtZ0ZUNmxuTVlFNEFKQjJVd004anM3QVR1bG5JOHRCNGdacDRocDBkUDNFSE5Rb0lNL2pzV3ZPc1dMWEtrYWhFUXNzcFQxNTIwZnJiaU4wcHVIbWFOZWZJVTdsRE11dHUzM1BteG14bXBidVlTb25aSDJzN1QxUVlpcTU0SlEzWU4iLCJtYWMiOiI0MjVjNTJhYjJiMzM2M2IzY2YwYjc3YmFiYzk1OTc1OTFiMjBlZjk2ODllYTNiOTU0ZGM0MzUyYzVkNWEzNjY1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085767127\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}