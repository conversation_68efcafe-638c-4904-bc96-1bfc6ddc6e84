{"__meta": {"id": "X0d5156b2ca24f39b93de70c34f2727b2", "datetime": "2025-06-16 09:53:46", "utime": **********.26693, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067624.330159, "end": **********.26697, "duration": 1.9368109703063965, "duration_str": "1.94s", "measures": [{"label": "Booting", "start": 1750067624.330159, "relative_start": 0, "end": **********.042836, "relative_end": **********.042836, "duration": 1.712677001953125, "duration_str": "1.71s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.042864, "relative_start": 1.712705135345459, "end": **********.266974, "relative_end": 4.0531158447265625e-06, "duration": 0.22410988807678223, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162944, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01128, "accumulated_duration_str": "11.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.17099, "duration": 0.0081, "duration_str": "8.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.809}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.214131, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.809, "width_percent": 15.071}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.24065, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.879, "width_percent": 13.121}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-70390080 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-70390080\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1685533430 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1685533430\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1501763244 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1501763244\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-54640626 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=116a2vl%7C1750067612209%7C4%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFmMUovM0tGUmovQ28zUVUzT3VJM0E9PSIsInZhbHVlIjoiYlRqUEtlRGVoS2pkRVRyRnVzS0F1OU1mRS9ycUx5bFY5OGx6Q3ZUN1h3dGNtNzJzRGE1YWlVcEpzTUZVcDlTdDh1ZVRYOWswYVdOMjRIenFUSVZxcm9DWjI1RWlvM0tJZTkwRVBvdmRhZmVIU0ZZZjV6VDZLWVpETVgrdEhrcFdpVk5hOE5NRkd4bElQSnVJNUJ1dVBjUnBhR2p3bHFXS3RVc0U0SVpyL2lXeWcwMytuQUd6SXB0RTlEYm12dEIzdXJhMnZDYVR0dmJiZUUvODZOU3lVWXB3TEFDZ0JhZzE5VkJWQll1U1g5Y05IMitRV0RleG5NUVVrTGJUTm1JVENwUUhRVjM2M0tjYUtXYnEwNHdkUUJQbThLYTNjREh5SFB3UUxuanFZYm0vb3BYK0o4VFJqMUllYi93b21SZ25ybWl3dnJidVhjVExyUTFnMk9ZM0VnRW9DcmxaMFlXRnR4K0htMWg3MmhzUUtGd0JnSkNsQ0dBUDBSdHVpVFF2TytKRktYSTJHeW9nd2V2SGpCdFFNWTFGUFBBMWdXb3hrS2xWUE9vQlBXMndRUm9ZdHpUWG9yYSs2WGpoUENGVm5BYVRmK0pYOW1qTTd5RUVvZm1hc2Ewa1R2WU4zQlBEU29yT1AySXgxRTZqMVRGK2JLSWsrQ3h3U2ErZEFVc3oiLCJtYWMiOiI0ODI4ZjFkYTQxYmVjNDM4YTlhNTc5NGFiMzBhYWM1N2QyNjZiMDI0OTkyNzFkNDM1MDNkYjc2NWQyMWQzYjlhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjhJWXBhdjNyYmI3S3pmRTJIUitxMkE9PSIsInZhbHVlIjoiK1FOancwY0JMNEowQWtJRnV5UXlrdkNCTjBLMGc1b2NpWnNHQ1F1cEwvVmhWSlg3TnFIT21GTGhLSm4xZVM0cVFMU2tYSkRjTzhZZ1h6WlBFS2dhaURNVERONG9ycG10WVU5Tjdhb09EWDJYa1hpRHdSQ21xY3IvYUxFL0E0RDQ0WGZTTzdNTWkybllSdmlzOHhjMEZYTktiejlFTW5mdzJ0bG1zM2cvU3laZ0tadGgwSGdORFBoT3JUMzEvY0lUSk5rai9OL0g3VWM1NVJIbHlDSStsWVNTc1VBL0ljeFRRcHVaT01mNjQ1VFlJMk5IM3ZYSzlualI1bjNWaDRjSkNIU1dHSGZIdVQzUFVzVEthRVU4VFVkY1RjLzVrSGlxbVkyZjNESXdGK1ZBWjNJcXllakoyYzJ2OVB6bFdSVXlRbVVLSXBHdjZFVThwVmRxbllrUW5VbjJhZ2FNNEVRRzZFNysxdE9vcVBtVlg4N0NSSk9raHpwMTg5SzBjcXhsL1VvUk5RbmQ0WkFnTWswVjBTMjNnaklEZGNINHZiZktlSEY4QWpCdC9ERjIvM1RTa1dBVDBDbzJ1d1JWME1ZV3VNWU1DczRsSlpiUisvaEJ5YjVnWWJ5R2JjVFgzZlJhUzFqbHgvRnc5TjhJZkRraEVVZXhOckt1UkJoNGdmYXkiLCJtYWMiOiI2NWI1NDFiYmM1Nzg1MTc1MDg4ZjAxYTE4ZmU2NmRjMzI3NjAyODdhZDQxNDVlYjQwMjY2NmJlMDY0Y2E3NzA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-54640626\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1671095034 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1671095034\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-673042887 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:53:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVtUVRqT084TVJUOXRJaklIWEFzWnc9PSIsInZhbHVlIjoiMlhxRkFmeFMzbDJka0trUmRpMjl1RStEZTUvQ2g3SVJRNGFVL1RERjBRNVZnRmJOTG5uWlhMMWd1TVFlZU9EdTRvVnFESDZkNTR5c3FLckJ3amEwcExqNW5RWkVUNmlxcUxnTGJOWGNLVkdjWHNTeG5sblJMbmM4bkdyYUhoanJNSVlhVjRna2dPbUdHS0ZMcGxZSzNhTGlUcFJMMlhrSDJzVTczdC9yWWZsQXNSUDAzMjZsT0QvUjZwL2orSDdYdnFQYkZUY3pxS1VrYkl2dE9wYlpaR0tOR0d1a2svV1FZbktXUy85dmZqblArMGFRb29wNTJaYXZpdWtvcWRmT01pRXhsVHJzMWZoT2pWRVRsb0hQVHQrYURDa01VL0E1aWJ0cnNEU2tmcU5pdUVzVGJ3dVIzOE5OU0s1Uis4NnpNU3ZVcjR1SEhJWHFVVDBYZXBubEpqOHBZbGZMd2ZMVU5rV0J4TFppNkNrcm9LQStBVGJXdGUzZVVZM2JVNUo2K2NRbzRiMXFWZStDUWtkMm1DTlB2U2ZuanZaVDhFN3ZhT1hsakRaWXA1V3U0ZTdicVBacEp4NG5FZHo2OU5adHh0Qk12UFlEK2dnMXZxK2l1RFBDZm1yaG1TQTR6UXJQUy8xamhKQStIbXZVTHNxK25PMkNGTTNxQ01OTWU4dlkiLCJtYWMiOiIyMTQ3MzNiYjYyYzMzZmI3MmZhNGQ5YjdiNTZhZGRmYmMyYmIzZjdlOTA4ODQ5NmU0ZTg0ZDMxY2M2Yzk3NjAyIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikhpb1lkRXBZMmVobEdFVDNUVkprT1E9PSIsInZhbHVlIjoiQjZSNlJjS2NhVXlvZmxwYkYyc3RVZGgxWGYzckdMSzRuOGtjT2FMZDZxTXk1ZEFEUUVEREM4SWZqOUJyZ2NmVy9UT1hOZVRBUk5ZZHVoZkFXNEFvWEZWOXdhay9DTmdRdC80NGdzb0psSEU2VGYwcmJ3dXVqUkxSUVRvM281QXkxZ242OHlUa1VEMENuNytVWlJjUmxSRjNsc1NmTDN2VlVXdGdRN1RJVkNaNmRpTmllbUZneXRXQ05PNHhPa0xBMEdOcE1JbVlOSHRvTVBvQk9KUGJNSUNzWVVmOFlEZjBlVld3YklVekQ1eTlCM3lMdW1wcDJUM29kbEhOYUNkL1I3d0pCNXJubVpSWlViUmc3NGVKVThlaVdkRUJkd2ozTDltS244OXgxdlNYYXBycG5qdno0WEV0V0lDc3FqTVdLSFR4K3JncWtid1Q5OHNITC9USVgyQnRmV0JMclBlMHBhUTFwdDB2YnhnTWxqLzhNa2lkM2RhRFhEKzlxM3pCazNxelpHT1RpZHlkcUt3UTZRWXZQQitZSzJVR1ozaWhyL0tOa3pvUC8xVkJPaVI2L1huVEU4UGlyVVRLNW9pWkdWQnNHUnhaSzUwVEVwVGRLd3h5MGpqMEpiT0E0cEFJTEZRbjBQa2YvZ2x3S0FXUHcwZWpySHB6RkIzVm01b1IiLCJtYWMiOiI4NGMyYzI0Njc1ZDJhOTBmNjdiMDg2ZmE5MWE5NzA2MzEzMjJkNjQ5M2I5OTEzNDMwZDJlYzM4NmY2YzhiMTRkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVtUVRqT084TVJUOXRJaklIWEFzWnc9PSIsInZhbHVlIjoiMlhxRkFmeFMzbDJka0trUmRpMjl1RStEZTUvQ2g3SVJRNGFVL1RERjBRNVZnRmJOTG5uWlhMMWd1TVFlZU9EdTRvVnFESDZkNTR5c3FLckJ3amEwcExqNW5RWkVUNmlxcUxnTGJOWGNLVkdjWHNTeG5sblJMbmM4bkdyYUhoanJNSVlhVjRna2dPbUdHS0ZMcGxZSzNhTGlUcFJMMlhrSDJzVTczdC9yWWZsQXNSUDAzMjZsT0QvUjZwL2orSDdYdnFQYkZUY3pxS1VrYkl2dE9wYlpaR0tOR0d1a2svV1FZbktXUy85dmZqblArMGFRb29wNTJaYXZpdWtvcWRmT01pRXhsVHJzMWZoT2pWRVRsb0hQVHQrYURDa01VL0E1aWJ0cnNEU2tmcU5pdUVzVGJ3dVIzOE5OU0s1Uis4NnpNU3ZVcjR1SEhJWHFVVDBYZXBubEpqOHBZbGZMd2ZMVU5rV0J4TFppNkNrcm9LQStBVGJXdGUzZVVZM2JVNUo2K2NRbzRiMXFWZStDUWtkMm1DTlB2U2ZuanZaVDhFN3ZhT1hsakRaWXA1V3U0ZTdicVBacEp4NG5FZHo2OU5adHh0Qk12UFlEK2dnMXZxK2l1RFBDZm1yaG1TQTR6UXJQUy8xamhKQStIbXZVTHNxK25PMkNGTTNxQ01OTWU4dlkiLCJtYWMiOiIyMTQ3MzNiYjYyYzMzZmI3MmZhNGQ5YjdiNTZhZGRmYmMyYmIzZjdlOTA4ODQ5NmU0ZTg0ZDMxY2M2Yzk3NjAyIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikhpb1lkRXBZMmVobEdFVDNUVkprT1E9PSIsInZhbHVlIjoiQjZSNlJjS2NhVXlvZmxwYkYyc3RVZGgxWGYzckdMSzRuOGtjT2FMZDZxTXk1ZEFEUUVEREM4SWZqOUJyZ2NmVy9UT1hOZVRBUk5ZZHVoZkFXNEFvWEZWOXdhay9DTmdRdC80NGdzb0psSEU2VGYwcmJ3dXVqUkxSUVRvM281QXkxZ242OHlUa1VEMENuNytVWlJjUmxSRjNsc1NmTDN2VlVXdGdRN1RJVkNaNmRpTmllbUZneXRXQ05PNHhPa0xBMEdOcE1JbVlOSHRvTVBvQk9KUGJNSUNzWVVmOFlEZjBlVld3YklVekQ1eTlCM3lMdW1wcDJUM29kbEhOYUNkL1I3d0pCNXJubVpSWlViUmc3NGVKVThlaVdkRUJkd2ozTDltS244OXgxdlNYYXBycG5qdno0WEV0V0lDc3FqTVdLSFR4K3JncWtid1Q5OHNITC9USVgyQnRmV0JMclBlMHBhUTFwdDB2YnhnTWxqLzhNa2lkM2RhRFhEKzlxM3pCazNxelpHT1RpZHlkcUt3UTZRWXZQQitZSzJVR1ozaWhyL0tOa3pvUC8xVkJPaVI2L1huVEU4UGlyVVRLNW9pWkdWQnNHUnhaSzUwVEVwVGRLd3h5MGpqMEpiT0E0cEFJTEZRbjBQa2YvZ2x3S0FXUHcwZWpySHB6RkIzVm01b1IiLCJtYWMiOiI4NGMyYzI0Njc1ZDJhOTBmNjdiMDg2ZmE5MWE5NzA2MzEzMjJkNjQ5M2I5OTEzNDMwZDJlYzM4NmY2YzhiMTRkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673042887\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-310362042 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310362042\", {\"maxDepth\":0})</script>\n"}}