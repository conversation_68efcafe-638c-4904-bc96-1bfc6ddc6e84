{"__meta": {"id": "X06e8a0c1660157091b952653b8f14356", "datetime": "2025-06-16 08:51:15", "utime": **********.882081, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063874.602532, "end": **********.882126, "duration": 1.2795941829681396, "duration_str": "1.28s", "measures": [{"label": "Booting", "start": 1750063874.602532, "relative_start": 0, "end": **********.755933, "relative_end": **********.755933, "duration": 1.1534011363983154, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.755953, "relative_start": 1.15342116355896, "end": **********.88213, "relative_end": 3.814697265625e-06, "duration": 0.1261768341064453, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43481600, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.015359999999999999, "accumulated_duration_str": "15.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.842181, "duration": 0.015359999999999999, "duration_str": "15.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ipptOR5SvJPEqO28raB9EYwoiv6NfH65r9gsEzDJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1658138185 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1658138185\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2140577420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2140577420\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1264153544 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264153544\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2095702943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2095702943\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1277357251 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:51:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVmZUlJRm1zMTNVQnNFTUlDNE5PSWc9PSIsInZhbHVlIjoiRUpnYXFrZnhpMTRvcnRIQkRWZUxFd3dWV1ZDbzQ0bEVpM3lPMnNsMDFOSG1OYm9CL2dNeTJad0FYQ1I1WmgwM0JKOEdWSWhjL1B0TzVHSzZ6Q2FzWmJSY2hHR0ZJSDBPTmZhN1RoeWM3RkViaEVuMEhEd0s0bE4vVm1QSE5qN2g3ODR3TWxSMGE5Y1FCSzU1RHlTa3VmVGJTRzRGR1kwSEVKSXNMZG1tMGNiT0RsWnZqNzVmdnR2eFVPNGxyd1ppSDh6akFOY1dhSy94M1VqSEtmckplWHcrQUJXUm5DSEd3b21ZcExrOFNLeGJNbHBobkNJV3U0UFR0dHJsbFpGdVVGN3FLOVJHOHJNSXphOU05Ym1GUkM5WmVWQ1NMWWJkaTgrTmdqc1RtSHFFc3p6ZVRnMi9PdDlYZDBubzBhUXF3N0RteW1oMlozclVMN1JzWmlLWll3aWhSaFBvMlo5dEs1OU44SXZreWptWWhFV29pOGNwU0t6QldGNU9CWkZVUkNwejJMZ1VncTBwQ0ZmcHdlM014bzRFZFRUdnoxNy8xc01TV1ZRZzNodDR0c1EwRGJ5eWdUVXZsWnNacFE1MEEzTUZ2QXFqb2FPUklnQ1UxYUpRUFlwV0Y4cmlITnRZdnR4aDNXeEd4VUhVZytXWnllRGhRU2NBTGlKTlZEQ3IiLCJtYWMiOiJmNDg1YjdlZWNiZDFhYTc3Mjc0YTU3ZGMwMGI4ZDc0ZDkyYjc2M2U0MTZkZTg3MTBmYTNhYzEzOTI2MTEzMTRkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZXUER5T2dzd1RJTTF4UFhyK3daUVE9PSIsInZhbHVlIjoiZlkwVVVxL0JkZ2o2U1V1M1VwdlgzcVdvQis5Ym5MY3lvaGxRUk5ibGRrbjY3WWtVWEVnK2RINzZxZXl2QnF1RmRrWEFmeW42OStpQnNNbHUrOVByZnZPdDBCSTU1ZTljUlVvMW50OHVVSW43SHdXclQrS0hqTEMxN1JibEhhWUhkOUVYU21rd1ZjT0Y0RXJ4eStwd04xOFlsQzRLRHhaMVcxSERiM1FrWDlieHBCVmtwOWRpcTQwWTljakdSNThRTk1rY2xrc1dwZkJzeGRuK2FOcFl2YmVPRWxjbDRLa1grdVZvRjVzVFYwa2lHakp6UjVFd21FSGtkS1dlVzRjd2hpNTdmREVJN0RhWjlCeGtOMEJzcXJKWmRtVkFwZFdKeEZ3NUxRaFV0UUpkUkp2WXpsRDVwUEpRVi9UZGNNMFVZTTk4NGwra3JOSko2UXFNSVljbmVRZzVxYmp4Q0YxZ3YwMGtOaFhMK2JzR0owd2hEUW1PbmpSMXcxRmtEYU1GaHpSc1hXYVhWdkQva1VWM0diZEJibVRNS1J4WlRjUGNiTksvZHQvaEo5T0duY0xoS01aT0t6aGRmQit4dGVnQ2tOdUE3dTdEY0thVnBoU3dOSVVtQmlTbWIrSWlkaDZid3BPajByMTBJZFVqVmFLUEpnamROa1NQWW00RHp2VVQiLCJtYWMiOiI4OTc4OWE1YjY0ODdjYzgzMzkxNDFhN2U5MWZhOTU5MTYzNTlmYWJjM2UxOTI0YzM5ZmEwYTVmMWFjZWViOWJlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVmZUlJRm1zMTNVQnNFTUlDNE5PSWc9PSIsInZhbHVlIjoiRUpnYXFrZnhpMTRvcnRIQkRWZUxFd3dWV1ZDbzQ0bEVpM3lPMnNsMDFOSG1OYm9CL2dNeTJad0FYQ1I1WmgwM0JKOEdWSWhjL1B0TzVHSzZ6Q2FzWmJSY2hHR0ZJSDBPTmZhN1RoeWM3RkViaEVuMEhEd0s0bE4vVm1QSE5qN2g3ODR3TWxSMGE5Y1FCSzU1RHlTa3VmVGJTRzRGR1kwSEVKSXNMZG1tMGNiT0RsWnZqNzVmdnR2eFVPNGxyd1ppSDh6akFOY1dhSy94M1VqSEtmckplWHcrQUJXUm5DSEd3b21ZcExrOFNLeGJNbHBobkNJV3U0UFR0dHJsbFpGdVVGN3FLOVJHOHJNSXphOU05Ym1GUkM5WmVWQ1NMWWJkaTgrTmdqc1RtSHFFc3p6ZVRnMi9PdDlYZDBubzBhUXF3N0RteW1oMlozclVMN1JzWmlLWll3aWhSaFBvMlo5dEs1OU44SXZreWptWWhFV29pOGNwU0t6QldGNU9CWkZVUkNwejJMZ1VncTBwQ0ZmcHdlM014bzRFZFRUdnoxNy8xc01TV1ZRZzNodDR0c1EwRGJ5eWdUVXZsWnNacFE1MEEzTUZ2QXFqb2FPUklnQ1UxYUpRUFlwV0Y4cmlITnRZdnR4aDNXeEd4VUhVZytXWnllRGhRU2NBTGlKTlZEQ3IiLCJtYWMiOiJmNDg1YjdlZWNiZDFhYTc3Mjc0YTU3ZGMwMGI4ZDc0ZDkyYjc2M2U0MTZkZTg3MTBmYTNhYzEzOTI2MTEzMTRkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZXUER5T2dzd1RJTTF4UFhyK3daUVE9PSIsInZhbHVlIjoiZlkwVVVxL0JkZ2o2U1V1M1VwdlgzcVdvQis5Ym5MY3lvaGxRUk5ibGRrbjY3WWtVWEVnK2RINzZxZXl2QnF1RmRrWEFmeW42OStpQnNNbHUrOVByZnZPdDBCSTU1ZTljUlVvMW50OHVVSW43SHdXclQrS0hqTEMxN1JibEhhWUhkOUVYU21rd1ZjT0Y0RXJ4eStwd04xOFlsQzRLRHhaMVcxSERiM1FrWDlieHBCVmtwOWRpcTQwWTljakdSNThRTk1rY2xrc1dwZkJzeGRuK2FOcFl2YmVPRWxjbDRLa1grdVZvRjVzVFYwa2lHakp6UjVFd21FSGtkS1dlVzRjd2hpNTdmREVJN0RhWjlCeGtOMEJzcXJKWmRtVkFwZFdKeEZ3NUxRaFV0UUpkUkp2WXpsRDVwUEpRVi9UZGNNMFVZTTk4NGwra3JOSko2UXFNSVljbmVRZzVxYmp4Q0YxZ3YwMGtOaFhMK2JzR0owd2hEUW1PbmpSMXcxRmtEYU1GaHpSc1hXYVhWdkQva1VWM0diZEJibVRNS1J4WlRjUGNiTksvZHQvaEo5T0duY0xoS01aT0t6aGRmQit4dGVnQ2tOdUE3dTdEY0thVnBoU3dOSVVtQmlTbWIrSWlkaDZid3BPajByMTBJZFVqVmFLUEpnamROa1NQWW00RHp2VVQiLCJtYWMiOiI4OTc4OWE1YjY0ODdjYzgzMzkxNDFhN2U5MWZhOTU5MTYzNTlmYWJjM2UxOTI0YzM5ZmEwYTVmMWFjZWViOWJlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277357251\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-782588319 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ipptOR5SvJPEqO28raB9EYwoiv6NfH65r9gsEzDJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782588319\", {\"maxDepth\":0})</script>\n"}}