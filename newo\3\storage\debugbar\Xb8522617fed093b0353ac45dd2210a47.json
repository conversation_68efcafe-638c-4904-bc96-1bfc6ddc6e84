{"__meta": {"id": "Xb8522617fed093b0353ac45dd2210a47", "datetime": "2025-06-16 09:53:49", "utime": **********.125178, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067627.319907, "end": **********.125213, "duration": 1.8053059577941895, "duration_str": "1.81s", "measures": [{"label": "Booting", "start": 1750067627.319907, "relative_start": 0, "end": 1750067628.909413, "relative_end": 1750067628.909413, "duration": 1.5895061492919922, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750067628.909438, "relative_start": 1.5895309448242188, "end": **********.125217, "relative_end": 4.0531158447265625e-06, "duration": 0.21577906608581543, "duration_str": "216ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022699999999999998, "accumulated_duration_str": "22.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.016842, "duration": 0.01954, "duration_str": "19.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.079}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0674129, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.079, "width_percent": 5.639}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.090336, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.718, "width_percent": 8.282}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1986200256 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1986200256\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-58618025 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-58618025\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-376598050 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376598050\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-310444229 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=116a2vl%7C1750067612209%7C4%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpzT3hlT1AzdlczTzQzdERhNzFvaVE9PSIsInZhbHVlIjoiV2pSV1pPaTBtUE1yYmN0VXpENngwOXh2WlpSVlFUbE9hS1pQVnZ3ZEVrZ2dSWGdWaCszM2ttT0hrMDkyVkxOVzZsaDNhSkJ4VXp4WGk2Vk9nUWtqZGcrd3NNRjBDQUhITmM0TVpWM2ZVSmY1dnJ5dU9Mczh3bXp5VC9lTUd1bDRlTlN0dG5KWWgyK2UvQ0hPSUQ4dHdoeEN6SFNJdEg3VkhWMlZGZTUrQWFKS29rVkdZZU5vTVQ1QTRQZlZvV3I5L1p5SlFSMHQ3Q0tWai9OTmVRWmFySjVMVUxlOFI2Nm5rTm1IWEQrRDFHdlZlSjJqWFVJUkFWVk93aGJMZVhJUDZpSW1aaUdSa2RiS0Q1SjVlR2Z0RWNYMzgzS0N2Q2RkbGFPa1V6K0RKNVlMdFF6b1pTYzFzTkFJRkFNeDI5bkJuUnJ2STF0NUZGNjNhRkpDdy9CUkN0UXZRUWo3eDlXL0d5bFRydWtCdlhBZ2JKTE9XNFNEUGNPWnorSHlZNCswdC83SkZQRS9ZeWRDejlVanJGVjFSNndiSW90R3doQ0d6YkNrb1J6K1RoZ1JzdDNsbUVtYVRNVnd2eGpySmM1TWRIa1hITmYvRTlEbkhZR09VdmVOWFRZcVJQaGQrZ00rMWdmWXhHNEdlSGM2SzB0TFY1MEE3RFlxa25LTGdHU2oiLCJtYWMiOiJlZGI3YmMyODU1OTU5ZjBlOTk4ZDQ5OTRiMzJiZjE2YjU2OWI4NjVhMGY3YmMwZGE5YTdlMjRjYWY4MGM5YjE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlMzZlc4KzEzdDJrdmZ4ZXVZUUVPWkE9PSIsInZhbHVlIjoiQVZWTnVWNzhibmU2YzhNaXFrZm1KLzRoYStrSlJCdGg4MGFyTmkrQjcwSjdyZC9xNTBpQnRhTTYvRVlIbFJ4SmI4WWtHektaVklUbGFCd25GaUppRGdKenZzVlFkWXRoVDN0Vm9kWWNlNHI3dCtBZmRMbjE2ZUgvZ1JVTUVVQjROTHlRd0E0VkRHa3NKZzczR3FyOVpGVk9nNCtablZwYkN4czcvRmRuNzAvblR3UFZFSnZyOXJEcTh5bDY5NjZ6a2xNWUpMQ0NmZ1hpamlQQlBhKzZkc2xwR2hxYytQaWJkS0o4akx3TDJsNXVQdEI1K2VHcExZVnhxbHlqczhkWGJBSEdVMU5rYVBBRklNcFpNMjgrRDF3OVE4Z0hJREZIa3pId08xS2lVYVVBMDd3bFU0NmlWMXEzb0RRWS94QVpoQm5pc3pVRWZMZ1lrZTdjMUVJYmVtTUhGWjFLd29jVDl1SXhRRGhRZHZaeWd3SlJpcUUzM0hYQUY4QVp6Q2ZZVWZDa0p1NE9vbzYrcUlqemwvTHhzQVVsQVBSUHVJOEJBSVhCcW8zdnpwU1N5SEc3T1pNUlA1bEMvSlhaUlJtMkIrMlhnei8rMm9ES1E0QW1nRjlSNUNhYXVxNko4aFQ5WDJGVkxiUDRHSzR5Wk1mUktMRVB4NGdlQ2RIU0pYclEiLCJtYWMiOiIyZjRlYTdlMWJiNGM3MDYxM2ZjNTQyMDA4MGJkOTM3YzgzZGJhNmI2ZTUzMzQwZjdiNWRlMjE0OGE2ZjY1ZjViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310444229\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-142681357 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142681357\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1297222765 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:53:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVOWUFDZk42V2xUMXRCQXdDMzAwSGc9PSIsInZhbHVlIjoieWdpUDY0c0VHZCsrM3owQnJXYS9hckxOT2M3L1p4U2UxOHd4QzUxcGtaTTJNKzdQdWg1ZW1rUElqMEU2NTJXTUl0OGdMQU54dWMxVnRobm9sNjdzWU9ZcC9Xak5ZaDZ5Y3hrVng5Mk5FSHFabG14TGU4alBhRDhsNXg2RTlkZUFla2l2eFRqYk5rTkFXendkUThjWkprblhCVE4yRG93UDZUc0YyTE5wRjR0RmNOMjNyWUc0TW4yelVMK21PaFVZUk1OeFNxZUdhNk04Y2wwYzJQMUY4bGprNFNtbXRyelJ0bEtBaGs3VkduTzlXaU9MUWdvL1RzVnluTzJ2Z0JWSFk4S0dIVG5XaGd4bEUvQlA0d0hYdG5wdDBSTG5Wd1pBTFEzbUdOMlRYeTFkTUQ1UUg3U0hHakhRY0ZJVlkzbnppVGJaQnU0K3hLRmM3OTB0dW9zSlc1YmU1UFBRZnI0RlpTdWZtVS9EOWIzSzFFVGFmMGcyVnEraUlPMmhIVnl5WWo5QkU3UHJlNzh6TDQvOTFZWU5KQ3Y5OUJxOGZhRDRyWkVpS1gxazhadUVWSDZPSHl3OHNlU2JWc3BlUDR1cHpTRURMSDV4ZjBob0NIbHVqNzZSWWV4Y0VkdmcxZ1JCQkRiNXBJSk9pU1JzZm43ZmJiYlREY1ZaWVFJYWdrZFIiLCJtYWMiOiI2YWIyN2QzZjhiYWRiYWIwYjY0NGE1MTEwMWNhOThmY2U3NTU5YTE4MTJjZjY2NWY1YTNiZWY0MGIxZWNjMWU0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJOMUNOQ3JYbWVuRjhWaVlYZVZoR2c9PSIsInZhbHVlIjoiVVdJWEYrd2tVTHJ1QStNMDJ6cVlTY3NiNzQ2NmRidG1VeGdhUHN2T0pHUGxBWUlJc0tRMktBNXg4Qk50c2lRQjFSU3F2WElHa0xIOU5iTlY1Wm1tWVM0TlNPcWNKcDczUm56OUlzbllYb3A1YmFKNGJpUU9aeENEdkI3dE5aZUFPaXJnbVlYb1dxbFd1YlM0SU93Q0kyODQ3bTVvZWs1L05tVm9Lbmt5TnNFN09mYXVkYVJzbHg1Tm9YaTEzS1duTGxSaGJpWExLMU43UVozVUxWcVJGOEJSdmlSTHk0eWN0dDNuNEdKaWdOZyt1QWRTeFZ2TUVBVnhDTHVacUdkblpqUEFpU29QT211dTlSSjh4czZmVFVSMkUwMmQ5L05sRjAzVDIzWnNDUWUzdWFkRzNEby9RMmJGQzBLNjZwZ2VOMXl1QlhiTmZUMDllcXozZWJuVDN2Z0tTQnRzcjZBRm1TVUhxOHg2QWRsTndJbnZhejZ4Z3QwUlhDVHRBTFZHdVg4RnhlaWhFcE9kTzJTVUZQbVlYQmkzTUhIL2IzOEh6YzJEM00xSkIvN3FIMzFtWXArMjN0a0NPTDM1emFSRG4wUGFTcmJZK09EMkZFbEtmWDRDN1FhaEg2c1FwYWRZaENVOE5KRENoQ3RzN3V6YWxIekdnRFhZL2pHb2NCWjAiLCJtYWMiOiJjNWE0ZjI3MzZkMTc4MjA2OGViOGViYjlmOGY5ZDhmM2ZkODFlZmE4YWUzODk1ZmU4MjIwZDM4ZDBiNjBkOGZiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVOWUFDZk42V2xUMXRCQXdDMzAwSGc9PSIsInZhbHVlIjoieWdpUDY0c0VHZCsrM3owQnJXYS9hckxOT2M3L1p4U2UxOHd4QzUxcGtaTTJNKzdQdWg1ZW1rUElqMEU2NTJXTUl0OGdMQU54dWMxVnRobm9sNjdzWU9ZcC9Xak5ZaDZ5Y3hrVng5Mk5FSHFabG14TGU4alBhRDhsNXg2RTlkZUFla2l2eFRqYk5rTkFXendkUThjWkprblhCVE4yRG93UDZUc0YyTE5wRjR0RmNOMjNyWUc0TW4yelVMK21PaFVZUk1OeFNxZUdhNk04Y2wwYzJQMUY4bGprNFNtbXRyelJ0bEtBaGs3VkduTzlXaU9MUWdvL1RzVnluTzJ2Z0JWSFk4S0dIVG5XaGd4bEUvQlA0d0hYdG5wdDBSTG5Wd1pBTFEzbUdOMlRYeTFkTUQ1UUg3U0hHakhRY0ZJVlkzbnppVGJaQnU0K3hLRmM3OTB0dW9zSlc1YmU1UFBRZnI0RlpTdWZtVS9EOWIzSzFFVGFmMGcyVnEraUlPMmhIVnl5WWo5QkU3UHJlNzh6TDQvOTFZWU5KQ3Y5OUJxOGZhRDRyWkVpS1gxazhadUVWSDZPSHl3OHNlU2JWc3BlUDR1cHpTRURMSDV4ZjBob0NIbHVqNzZSWWV4Y0VkdmcxZ1JCQkRiNXBJSk9pU1JzZm43ZmJiYlREY1ZaWVFJYWdrZFIiLCJtYWMiOiI2YWIyN2QzZjhiYWRiYWIwYjY0NGE1MTEwMWNhOThmY2U3NTU5YTE4MTJjZjY2NWY1YTNiZWY0MGIxZWNjMWU0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJOMUNOQ3JYbWVuRjhWaVlYZVZoR2c9PSIsInZhbHVlIjoiVVdJWEYrd2tVTHJ1QStNMDJ6cVlTY3NiNzQ2NmRidG1VeGdhUHN2T0pHUGxBWUlJc0tRMktBNXg4Qk50c2lRQjFSU3F2WElHa0xIOU5iTlY1Wm1tWVM0TlNPcWNKcDczUm56OUlzbllYb3A1YmFKNGJpUU9aeENEdkI3dE5aZUFPaXJnbVlYb1dxbFd1YlM0SU93Q0kyODQ3bTVvZWs1L05tVm9Lbmt5TnNFN09mYXVkYVJzbHg1Tm9YaTEzS1duTGxSaGJpWExLMU43UVozVUxWcVJGOEJSdmlSTHk0eWN0dDNuNEdKaWdOZyt1QWRTeFZ2TUVBVnhDTHVacUdkblpqUEFpU29QT211dTlSSjh4czZmVFVSMkUwMmQ5L05sRjAzVDIzWnNDUWUzdWFkRzNEby9RMmJGQzBLNjZwZ2VOMXl1QlhiTmZUMDllcXozZWJuVDN2Z0tTQnRzcjZBRm1TVUhxOHg2QWRsTndJbnZhejZ4Z3QwUlhDVHRBTFZHdVg4RnhlaWhFcE9kTzJTVUZQbVlYQmkzTUhIL2IzOEh6YzJEM00xSkIvN3FIMzFtWXArMjN0a0NPTDM1emFSRG4wUGFTcmJZK09EMkZFbEtmWDRDN1FhaEg2c1FwYWRZaENVOE5KRENoQ3RzN3V6YWxIekdnRFhZL2pHb2NCWjAiLCJtYWMiOiJjNWE0ZjI3MzZkMTc4MjA2OGViOGViYjlmOGY5ZDhmM2ZkODFlZmE4YWUzODk1ZmU4MjIwZDM4ZDBiNjBkOGZiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297222765\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1989715958 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989715958\", {\"maxDepth\":0})</script>\n"}}