{"__meta": {"id": "Xe3c86213cffc579227ba458c77efd2f2", "datetime": "2025-06-16 08:51:17", "utime": **********.345104, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063875.896559, "end": **********.345144, "duration": 1.448585033416748, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": 1750063875.896559, "relative_start": 0, "end": **********.07298, "relative_end": **********.07298, "duration": 1.1764209270477295, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.072998, "relative_start": 1.1764390468597412, "end": **********.345148, "relative_end": 4.0531158447265625e-06, "duration": 0.27215003967285156, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46084848, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.220729, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.239213, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.305222, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.318532, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.02715, "accumulated_duration_str": "27.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.16181, "duration": 0.00613, "duration_str": "6.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 22.578}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.174294, "duration": 0.011810000000000001, "duration_str": "11.81ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 22.578, "width_percent": 43.499}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.193541, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 66.077, "width_percent": 4.53}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2225878, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 70.608, "width_percent": 6.041}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.241651, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 76.648, "width_percent": 4.678}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.271568, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 81.326, "width_percent": 5.23}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.282238, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 86.556, "width_percent": 4.383}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.289113, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 90.939, "width_percent": 4.899}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.309826, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.838, "width_percent": 4.162}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ipptOR5SvJPEqO28raB9EYwoiv6NfH65r9gsEzDJ", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-191200379 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-191200379\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-471684006 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-471684006\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2129525656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2129525656\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1458724669 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1734 characters\">XSRF-TOKEN=eyJpdiI6ImVmZUlJRm1zMTNVQnNFTUlDNE5PSWc9PSIsInZhbHVlIjoiRUpnYXFrZnhpMTRvcnRIQkRWZUxFd3dWV1ZDbzQ0bEVpM3lPMnNsMDFOSG1OYm9CL2dNeTJad0FYQ1I1WmgwM0JKOEdWSWhjL1B0TzVHSzZ6Q2FzWmJSY2hHR0ZJSDBPTmZhN1RoeWM3RkViaEVuMEhEd0s0bE4vVm1QSE5qN2g3ODR3TWxSMGE5Y1FCSzU1RHlTa3VmVGJTRzRGR1kwSEVKSXNMZG1tMGNiT0RsWnZqNzVmdnR2eFVPNGxyd1ppSDh6akFOY1dhSy94M1VqSEtmckplWHcrQUJXUm5DSEd3b21ZcExrOFNLeGJNbHBobkNJV3U0UFR0dHJsbFpGdVVGN3FLOVJHOHJNSXphOU05Ym1GUkM5WmVWQ1NMWWJkaTgrTmdqc1RtSHFFc3p6ZVRnMi9PdDlYZDBubzBhUXF3N0RteW1oMlozclVMN1JzWmlLWll3aWhSaFBvMlo5dEs1OU44SXZreWptWWhFV29pOGNwU0t6QldGNU9CWkZVUkNwejJMZ1VncTBwQ0ZmcHdlM014bzRFZFRUdnoxNy8xc01TV1ZRZzNodDR0c1EwRGJ5eWdUVXZsWnNacFE1MEEzTUZ2QXFqb2FPUklnQ1UxYUpRUFlwV0Y4cmlITnRZdnR4aDNXeEd4VUhVZytXWnllRGhRU2NBTGlKTlZEQ3IiLCJtYWMiOiJmNDg1YjdlZWNiZDFhYTc3Mjc0YTU3ZGMwMGI4ZDc0ZDkyYjc2M2U0MTZkZTg3MTBmYTNhYzEzOTI2MTEzMTRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlZXUER5T2dzd1RJTTF4UFhyK3daUVE9PSIsInZhbHVlIjoiZlkwVVVxL0JkZ2o2U1V1M1VwdlgzcVdvQis5Ym5MY3lvaGxRUk5ibGRrbjY3WWtVWEVnK2RINzZxZXl2QnF1RmRrWEFmeW42OStpQnNNbHUrOVByZnZPdDBCSTU1ZTljUlVvMW50OHVVSW43SHdXclQrS0hqTEMxN1JibEhhWUhkOUVYU21rd1ZjT0Y0RXJ4eStwd04xOFlsQzRLRHhaMVcxSERiM1FrWDlieHBCVmtwOWRpcTQwWTljakdSNThRTk1rY2xrc1dwZkJzeGRuK2FOcFl2YmVPRWxjbDRLa1grdVZvRjVzVFYwa2lHakp6UjVFd21FSGtkS1dlVzRjd2hpNTdmREVJN0RhWjlCeGtOMEJzcXJKWmRtVkFwZFdKeEZ3NUxRaFV0UUpkUkp2WXpsRDVwUEpRVi9UZGNNMFVZTTk4NGwra3JOSko2UXFNSVljbmVRZzVxYmp4Q0YxZ3YwMGtOaFhMK2JzR0owd2hEUW1PbmpSMXcxRmtEYU1GaHpSc1hXYVhWdkQva1VWM0diZEJibVRNS1J4WlRjUGNiTksvZHQvaEo5T0duY0xoS01aT0t6aGRmQit4dGVnQ2tOdUE3dTdEY0thVnBoU3dOSVVtQmlTbWIrSWlkaDZid3BPajByMTBJZFVqVmFLUEpnamROa1NQWW00RHp2VVQiLCJtYWMiOiI4OTc4OWE1YjY0ODdjYzgzMzkxNDFhN2U5MWZhOTU5MTYzNTlmYWJjM2UxOTI0YzM5ZmEwYTVmMWFjZWViOWJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458724669\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2004361614 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ipptOR5SvJPEqO28raB9EYwoiv6NfH65r9gsEzDJ</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zS3GyHFb0YHURx1ejgftTbLO35h0UYvlNr3AM5ZQ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004361614\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:51:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdHZE5oTFBNNDFkKy9aUW9sRmlrUXc9PSIsInZhbHVlIjoicXVmZU9ic2pBUzE1cXowQzBjbk9DTmQvYm5GSUJtN1Z2VjNEbmFpWFRTcHMra21aTXFlVkFlRmxEYzY4S3hmUWNER0QvYWpZRDlTZm9pdEZWbUhUYVRyUVNrSWhneXlYSDc5VnRnYVQxcDcybjluZ1J6Y0NVSGN2U0RtWEEwV3ZBckgvSnVkWk03NHFRdVhvU3pOK0REUGNTTUFXUExsQ3ZHdldQSFZuM2xxc0p3N2hnblJaeGxHUEx1RVN0blB5by9QcC9STVdZdmpUUEp1T0FIZHk1RjdLRzU2S2JFWkFSRWh5dnh3Y3JLUUYvN0pnYmpYT3kvQTZKcE1mL3VjcDFWOXZ1czBLS0xIZVg2TUdMaU9nU01ySnNiV0tMakd6RUkzVmhSbGtkSVVzN21iQnNIaTBjQnI5dTdnMSs0WGNhSnB3MjhMUUkwSS9ZZFV0bHJlRnpvcFk4NTY1M2FGMjF0VzUyK2ZTYUxoS2FUcmRsV0kxTGYyTDQ3N20xRVZKMm9oU1hzV3ZHM3R2YUpLY1lIdS9hcm9VdlVmOWRPWmJrOFBidllmZVczKzR5VzlDazhyMEgwYlpHYjkxdE0vanJDTjlpYm1oR0dEV3hENVZ5ZzFxc3JSaVRvNnVxaFRjOTl6VlJma3VkeDJxTWFCUFNOd2xseFRoVGxkMzArQW8iLCJtYWMiOiJiMjg5MzAyNzc3NWE1NDdjY2E1ZGRjNmM1NGIwNDEzN2ExN2E4YjYyYjM2NDc2MjRiNjUzMTdmODNkNzM3NzI5IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldKSUkydUcyWFlUbldqWTF3UnJYY3c9PSIsInZhbHVlIjoiWnlJR3hkeFZIbnhzbm9Mdks4YW1RTHAvK2c4aDNnTGkvTko1Z2dLVjVOT3I2R0s4YlJuVFMwTHVSVitvVVRNNVdXS00yczduRVRnR3lpN2N1R0NQazd3T2lyNlg5NnVaTEFVbmRqOFdMMzRNallmaWt0c3g4UzZKaE1jeitLY2xBZnJnQmtOWFpKbW52dXZKaVgxY24rTUJWbWtrT29kU21pOUVBYWdYSWU1VkdqRllON2gyTmhTZXpSYm8yN1AvQUo3aklSaGx6b0tHNE9LSDhZMDdtd1VKTXBsWjlSMzFCbWM2NTFlWTFMUDNhb2dvMEtMeDY2V2FZOUlnOG91WUtJK1hvbUMzNnh1QldQOWdKVHlLeG1ZaFdXZSs1YzFMdVlzMkZtZWZvTGwvM01EaVF3QVNqdnBSL1UrQXVaTzlUUFF3Y2kxeXhpY2lJWXZqUTZmaFF0OUR3eVlPR3FsNTZyNE5oVkYyUnFxTW9DOUxNS1VTN0hyUWN4LzVKMzlvMkM5TUF5Snh0c1lHYkdqaUJmc1pJK1Z4Y1pvYnZJQ3JxMGtIaTFxeDJId2o4byt4TUJTRE04aXREQnNJSUJFSElsV2pwTWpteFRheTdJeXAyUUVCN1QzbHZ5Y1RVdjR1VHlPRXJHY1VudURUZ3dQWWczdHgwY0pIQ1h4RnVlVloiLCJtYWMiOiI4NDBiMzc3YWQwNTFhZDk1ZjJkZGNlMTg5ZTFmNGY1ZmUzZjJkY2NlMDNhODkyZDJiZjBlY2IxZTVmODdjZDc2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdHZE5oTFBNNDFkKy9aUW9sRmlrUXc9PSIsInZhbHVlIjoicXVmZU9ic2pBUzE1cXowQzBjbk9DTmQvYm5GSUJtN1Z2VjNEbmFpWFRTcHMra21aTXFlVkFlRmxEYzY4S3hmUWNER0QvYWpZRDlTZm9pdEZWbUhUYVRyUVNrSWhneXlYSDc5VnRnYVQxcDcybjluZ1J6Y0NVSGN2U0RtWEEwV3ZBckgvSnVkWk03NHFRdVhvU3pOK0REUGNTTUFXUExsQ3ZHdldQSFZuM2xxc0p3N2hnblJaeGxHUEx1RVN0blB5by9QcC9STVdZdmpUUEp1T0FIZHk1RjdLRzU2S2JFWkFSRWh5dnh3Y3JLUUYvN0pnYmpYT3kvQTZKcE1mL3VjcDFWOXZ1czBLS0xIZVg2TUdMaU9nU01ySnNiV0tMakd6RUkzVmhSbGtkSVVzN21iQnNIaTBjQnI5dTdnMSs0WGNhSnB3MjhMUUkwSS9ZZFV0bHJlRnpvcFk4NTY1M2FGMjF0VzUyK2ZTYUxoS2FUcmRsV0kxTGYyTDQ3N20xRVZKMm9oU1hzV3ZHM3R2YUpLY1lIdS9hcm9VdlVmOWRPWmJrOFBidllmZVczKzR5VzlDazhyMEgwYlpHYjkxdE0vanJDTjlpYm1oR0dEV3hENVZ5ZzFxc3JSaVRvNnVxaFRjOTl6VlJma3VkeDJxTWFCUFNOd2xseFRoVGxkMzArQW8iLCJtYWMiOiJiMjg5MzAyNzc3NWE1NDdjY2E1ZGRjNmM1NGIwNDEzN2ExN2E4YjYyYjM2NDc2MjRiNjUzMTdmODNkNzM3NzI5IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldKSUkydUcyWFlUbldqWTF3UnJYY3c9PSIsInZhbHVlIjoiWnlJR3hkeFZIbnhzbm9Mdks4YW1RTHAvK2c4aDNnTGkvTko1Z2dLVjVOT3I2R0s4YlJuVFMwTHVSVitvVVRNNVdXS00yczduRVRnR3lpN2N1R0NQazd3T2lyNlg5NnVaTEFVbmRqOFdMMzRNallmaWt0c3g4UzZKaE1jeitLY2xBZnJnQmtOWFpKbW52dXZKaVgxY24rTUJWbWtrT29kU21pOUVBYWdYSWU1VkdqRllON2gyTmhTZXpSYm8yN1AvQUo3aklSaGx6b0tHNE9LSDhZMDdtd1VKTXBsWjlSMzFCbWM2NTFlWTFMUDNhb2dvMEtMeDY2V2FZOUlnOG91WUtJK1hvbUMzNnh1QldQOWdKVHlLeG1ZaFdXZSs1YzFMdVlzMkZtZWZvTGwvM01EaVF3QVNqdnBSL1UrQXVaTzlUUFF3Y2kxeXhpY2lJWXZqUTZmaFF0OUR3eVlPR3FsNTZyNE5oVkYyUnFxTW9DOUxNS1VTN0hyUWN4LzVKMzlvMkM5TUF5Snh0c1lHYkdqaUJmc1pJK1Z4Y1pvYnZJQ3JxMGtIaTFxeDJId2o4byt4TUJTRE04aXREQnNJSUJFSElsV2pwTWpteFRheTdJeXAyUUVCN1QzbHZ5Y1RVdjR1VHlPRXJHY1VudURUZ3dQWWczdHgwY0pIQ1h4RnVlVloiLCJtYWMiOiI4NDBiMzc3YWQwNTFhZDk1ZjJkZGNlMTg5ZTFmNGY1ZmUzZjJkY2NlMDNhODkyZDJiZjBlY2IxZTVmODdjZDc2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-595691489 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ipptOR5SvJPEqO28raB9EYwoiv6NfH65r9gsEzDJ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595691489\", {\"maxDepth\":0})</script>\n"}}