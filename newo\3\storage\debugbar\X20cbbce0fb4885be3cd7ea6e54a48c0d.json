{"__meta": {"id": "X20cbbce0fb4885be3cd7ea6e54a48c0d", "datetime": "2025-06-16 08:50:35", "utime": **********.444184, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.965382, "end": **********.444216, "duration": 1.****************, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": **********.965382, "relative_start": 0, "end": **********.219765, "relative_end": **********.219765, "duration": 1.***************, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.219792, "relative_start": 1.****************, "end": **********.444219, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01854, "accumulated_duration_str": "18.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.328453, "duration": 0.01531, "duration_str": "15.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.578}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.371996, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.578, "width_percent": 8.037}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.420474, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.615, "width_percent": 9.385}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063832651%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRwK29yU1ZxSzFBVjFISTRYZWcxZUE9PSIsInZhbHVlIjoiWDNsbkg0QmtVdExrbjVlUWpENUxuT0RvMzZySTBFMFBQejVJK3ljbXU0V3UyTnBmUUNoQVgwYkFVangxVk9SdjgrakNMYUtFTUcwTmFEVzJUZWN6Z0E3RTN4blp3WXh1L3hNZitSY3pPVEJSREYwKzdNV1JUTGNkc3QvUUNPcm43aEJGSHdvL1hrTDNDUE5zLzQ5RHVrbnd2NFRCZmpqeVYzU0FrUm1aakxDdHhBMXBwemhFUVZGYy8rTitobnpVMExzMk5sUHIySGhDY0xncjZqb1h1MGVBekd3ZUhnMEdvcEFmOXpuOWZodjY2V0xpL25CYlZaaFZuZ1N1NFdJZ3I1aFZNRURxenR5dXgvcGFObzZTblNCYmMwVDUwdnJFWWYvVy8zZzdNd1ZkSzJNSUNVVU1LNTVUY3FGM3dmTVJkQ25USzVDYkVnY0RXR2NKQ3RjMjFGQjNxTFlDNXpNZzdrY3h2SVBwTisxODViMTJiNHhqZWpHbE92Y1JGZTM2bEdnVVNKNko1RzQ1N1oydDFIU0srUW1UaUIzcEcwSmlMY0swclB6Qk5VV1l3QjZwcnRIRUNGNG51Zk53WG1mbkthU2FLYlVaSzcwanRYQzVxb0NvNTYzZ3YrM2xmYWdWYWliVnpDQ1Z0R242YytuMTcxM1FidW9BenFvY3EzcnQiLCJtYWMiOiI0YTE5ZGQzNTIzY2VhNzYzYTUxYjA5NzUzY2U4ZjZkMTJkNjFlMmM0NzFiOTdmZmNkMWVhMDJiMTg5MGJlOGRkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImUzREQ4cWlkcWpVMHp2OVMzUkF3emc9PSIsInZhbHVlIjoiWmdBeGdVcnpiR1M1M0VydU5IeHR6S3hMVWFrVm1SbjZvaVpmSWx0RlZUMzVEVzNuUk9GM2lya2prNnJvSWxvVmFBamJhN0Q2bFRmVkpwZEsycEpZa0wzRGFJSHlQK0VQbUhaM0FkQTJ4NFNaa1hsajN0bU50UFhoMUhPU1lXcGh1akZZSFZQazc4L1gxMEMvRnp6aWtLUngxR2MyN2FhWTF5RHl2TjNTeUhsKzJRMTA4dzN1aGtzYU5lNUlKWXRTekY1MnpTc2tqaWJYbWpkaUNwVkR6TS9WYVVlQlppUG5oM3plMXI2MEhRYVB3UG9jVzY2aEJLUEVjNmRtdFFjTTBFanFRcEo2Sml0R1N0SjJwcnpYTVBUZ3FIZ3I4SDhsc0JMRVhyMmxhZGhEUEVkaERPMHlZeEpOWFB4RDRQK1J0NE15eWJnTS9wbU5KSFdpWGxRNXVKK3FTcVZQalFIU0FIMjk2Sk9GVUNjR0JVVjdReVZxTTVUQTRMSjZwZENPZFZWOVk2VkVZZlN3Ukh4eTZsT29oclo0ZVpDRW8ybFV5Y1hBeG9paG9mVmVxWlNIYUdZcmhVd1VHK3NILzFuWjd5WWJJY01CZCtMc3BYd1RDK2hNbnQ5cnVCMVZRR2VPUjN3MU45YlVGSkhIL08zazEveG80ZVpKL1FqRmJEN3EiLCJtYWMiOiI3MzlhNDAzN2FhYzgyOTRjNmUzNWMwYTcyZTVhMGJlOGI1Njk5YzI4YTIzNTQyZTdhOTQ0YjlkMmUyZGZmMTgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xy8o1OLZEDaYyV6WX9XzzYzbw5zHFfm46SvuTNQ8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1104832599 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImV2MlJXM2NQNWhRYXNJL1BVZ2dyQVE9PSIsInZhbHVlIjoidmNESy9mQ1ZkOCt2RWRDdUlDTE1qMjRNa1dPT2I4bisrQysvVkNXYzk2ZnVGZTdaeUxOMEE1VGdnRFpZUWNCMnpXN0VGendzZzZrbWZLQldQcXdtMHlrdG9hVmhEZENueWpIdG1WL0hvTEl3aHhIQzM1aENVa3l1S3JLWFk0Q0lkdFFMQUFsY0VXc3VEaG5TLzA1NGpXNmg4K2RIU1I2bkdaakM4Sk1wVEg3Y21iVXd1Q0w3Q1RDbWNyZG41YVF6WHNJVDR1UUlSeFV2czFlOWFVSTVnTFVRcVMrZU95a1h1Y1Z2dXBHajFtakhQdUlYSEdML1N4aFdmNmp6dFd1bVBlYVZETjNOY0lWYWlqaXpHRDBkQk91eFZBQlpUYWxIRklpaFg0SXlmY09kZ3RTZ2hUWVpUYmdYeVZxcUZOR0liUVQxRnJ4ZFBTNFlhTFhiZjFrWlNIeG8rZXpHNnZyMW1EME9jL0hIMjM1WVhvNnhsTFQxVFFlNGhSUXJwTU1tclluTk9qMVpRa2h1djZlQjMxdDJmTlluWTJDVjE3S1JOTFVRQ0FtYS9DSGdwSXZtd2RnTjNRbnZzZTdmSG1OQ1lUSFJSMmx2OGh0S0lhVjJkQ1R1YXE1Vkw4dG9EMmdNT3loVWswUE95bjJLcWV5TS8xOTlTZkFqaEVSbWtKbHAiLCJtYWMiOiI0ZjYwNWFlMTUyMmE3NDdkNWZjMjRiNjE1YTQ1Mjc2NTg2MWIwYmQ4NGNhZDk4MDg1ZWI2ZDQ1NjkxYzZmMTM3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhpTnJtbjc2S3lpSldFeHNMZnBwVlE9PSIsInZhbHVlIjoieXRKUDE2bE4xSEVwdk16YXJIWmI0Wk95azdhVWhnNFBNTHY2VDJQQjVzZS9IZlRzak1XRHIxampNcThmQktlRktodUUyb3RlZmZrUjVnVDRnVW5zV21LSWc3TG43MTMwL09xZzN5VjJoK0oyK21DMXdONkZCZTV1SVk3RGtKOWNZRnIzRlFnNVlvOXU5bnZiUncweXVFY2txNXFaeXlVUXlZVHUrMWI5TUxyZXg0Slo0WUNBZC93VGp5ejBOcGd5SUtFRmdqY3h0ZVZERi9SaFFJVTl3ckFwdE5vRlo3blYxaDhPOXdOeEIzaDhkZVgvT0FjVVgveEs2NEt1MElWTHdDTStydGtRSGM1M0ZNSStaNFFPaTRFb2VUbWJqM1FNMnBCckJpNnI1VUlObjBoMjdDR2ExUndyVGRpSjRiWGltRjNmRnVxSWd0WDRTMEEzREZObDhhSVA0aE8wR2txQW83dzlIUEU2Tzd4NHpteGk2SjJaSCsvbHVvWEdwU1M2M2hQdDZrNHlPZitkcExFNmV4d1Bqd3N6czZsWEk3OUwxSGlHWWVsV1dVanVlcm5IMGR4STJkSm9nc241djZLOHJnYTMrbHZVT3k0Q2d0VEdVSm9rMnBYQ0lRUnlUUkkzQlJVcWNMa0dqZC9tR01yV0Fxd1g3QllRaVRBKzY2MlciLCJtYWMiOiI2ZDRkOWQ4MjJkNTJhZDJhNmQ2MDkzZWMyNWM1ZGU5MzQ4MmZmMjhlM2U2ODdlYWFkMmNkN2U3YWQwYjNlNDUwIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImV2MlJXM2NQNWhRYXNJL1BVZ2dyQVE9PSIsInZhbHVlIjoidmNESy9mQ1ZkOCt2RWRDdUlDTE1qMjRNa1dPT2I4bisrQysvVkNXYzk2ZnVGZTdaeUxOMEE1VGdnRFpZUWNCMnpXN0VGendzZzZrbWZLQldQcXdtMHlrdG9hVmhEZENueWpIdG1WL0hvTEl3aHhIQzM1aENVa3l1S3JLWFk0Q0lkdFFMQUFsY0VXc3VEaG5TLzA1NGpXNmg4K2RIU1I2bkdaakM4Sk1wVEg3Y21iVXd1Q0w3Q1RDbWNyZG41YVF6WHNJVDR1UUlSeFV2czFlOWFVSTVnTFVRcVMrZU95a1h1Y1Z2dXBHajFtakhQdUlYSEdML1N4aFdmNmp6dFd1bVBlYVZETjNOY0lWYWlqaXpHRDBkQk91eFZBQlpUYWxIRklpaFg0SXlmY09kZ3RTZ2hUWVpUYmdYeVZxcUZOR0liUVQxRnJ4ZFBTNFlhTFhiZjFrWlNIeG8rZXpHNnZyMW1EME9jL0hIMjM1WVhvNnhsTFQxVFFlNGhSUXJwTU1tclluTk9qMVpRa2h1djZlQjMxdDJmTlluWTJDVjE3S1JOTFVRQ0FtYS9DSGdwSXZtd2RnTjNRbnZzZTdmSG1OQ1lUSFJSMmx2OGh0S0lhVjJkQ1R1YXE1Vkw4dG9EMmdNT3loVWswUE95bjJLcWV5TS8xOTlTZkFqaEVSbWtKbHAiLCJtYWMiOiI0ZjYwNWFlMTUyMmE3NDdkNWZjMjRiNjE1YTQ1Mjc2NTg2MWIwYmQ4NGNhZDk4MDg1ZWI2ZDQ1NjkxYzZmMTM3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhpTnJtbjc2S3lpSldFeHNMZnBwVlE9PSIsInZhbHVlIjoieXRKUDE2bE4xSEVwdk16YXJIWmI0Wk95azdhVWhnNFBNTHY2VDJQQjVzZS9IZlRzak1XRHIxampNcThmQktlRktodUUyb3RlZmZrUjVnVDRnVW5zV21LSWc3TG43MTMwL09xZzN5VjJoK0oyK21DMXdONkZCZTV1SVk3RGtKOWNZRnIzRlFnNVlvOXU5bnZiUncweXVFY2txNXFaeXlVUXlZVHUrMWI5TUxyZXg0Slo0WUNBZC93VGp5ejBOcGd5SUtFRmdqY3h0ZVZERi9SaFFJVTl3ckFwdE5vRlo3blYxaDhPOXdOeEIzaDhkZVgvT0FjVVgveEs2NEt1MElWTHdDTStydGtRSGM1M0ZNSStaNFFPaTRFb2VUbWJqM1FNMnBCckJpNnI1VUlObjBoMjdDR2ExUndyVGRpSjRiWGltRjNmRnVxSWd0WDRTMEEzREZObDhhSVA0aE8wR2txQW83dzlIUEU2Tzd4NHpteGk2SjJaSCsvbHVvWEdwU1M2M2hQdDZrNHlPZitkcExFNmV4d1Bqd3N6czZsWEk3OUwxSGlHWWVsV1dVanVlcm5IMGR4STJkSm9nc241djZLOHJnYTMrbHZVT3k0Q2d0VEdVSm9rMnBYQ0lRUnlUUkkzQlJVcWNMa0dqZC9tR01yV0Fxd1g3QllRaVRBKzY2MlciLCJtYWMiOiI2ZDRkOWQ4MjJkNTJhZDJhNmQ2MDkzZWMyNWM1ZGU5MzQ4MmZmMjhlM2U2ODdlYWFkMmNkN2U3YWQwYjNlNDUwIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1104832599\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1934142394 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934142394\", {\"maxDepth\":0})</script>\n"}}