{"__meta": {"id": "X779e3574fe93c6c39d8995e00729fa07", "datetime": "2025-06-16 08:50:15", "utime": 1750063815.017208, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063813.443813, "end": 1750063815.017246, "duration": 1.5734329223632812, "duration_str": "1.57s", "measures": [{"label": "Booting", "start": 1750063813.443813, "relative_start": 0, "end": **********.791996, "relative_end": **********.791996, "duration": 1.3481829166412354, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.792359, "relative_start": 1.348546028137207, "end": 1750063815.017249, "relative_end": 3.0994415283203125e-06, "duration": 0.22488999366760254, "duration_str": "225ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43500000, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.03146, "accumulated_duration_str": "31.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.942484, "duration": 0.03146, "duration_str": "31.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GdAP3Ala05cLcMv8CjWhgdpBs1UQ8quTxt10NpT1", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1310753890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1310753890\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-86859156 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-86859156\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1169849414 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"194 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169849414\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1375731520 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375731520\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-962308889 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjU5RFJIZUtiWmlCRms4NXBGb1FyOGc9PSIsInZhbHVlIjoid0Nuc3h0bEltRGZRV0p5NkdWYWRqOEl6ZG55QVVSL0JhNXRTRWkzUUVuTk5OcWNMNHRmbHZQZHJQUlNDemdtRzZwTTYwbForQk8rVVcycmZGbHdtK1M1eGNGL0trMHAyaHA0bEZaQ0FpN3dMaFRCcUF3NlhIUzZZSGRIa1lmcnlKN3NtLzhQWGtGODIwMjJuQXRMMjhkdzVnTnhubFkvTmJNQS9qTUpJM0VIZzVUaFVwampqaVRmOFpuOTgrL21wLzRXbjd2d2NvQlRMUjJNdlkzTWxaVGU5Ti9GTVhzMk5CNlJiU0E2MlNuMmk3Q1d6MTdNanRablJrMHVsR2VKZXhCelUrTGtFVFVoamw5UGVLN2o4VWNlWkZnOVRzUTBCSTVqbFlMZmwrT2xYbG9zTTlEanhEL2pWLzdveGEyQytObThzRHZCQzhhSHB5dnR1Z3VON25VL21yZ21jZkJkeW9RSDRLN0tySmpWTEV5TFE4Tm9JVllSQ25ucVhuejA0b2xLYnJ1SC9yN1cxM0NBR0x1NS8relMxV2tzZnZmLzVoL0pSd1R1TjdaS0Q5Z1pqajNia3lRRjFEWW9kbGIwWGxGR3B0L2NKaEowYlBLTHluMWt5d2pXbUo3VUI0dkNJNWZpellhNEx0aVJURnhMTDhFQXY1SU1saTR4QUQwdS8iLCJtYWMiOiJkMjkzNDRkNjUxODQyYTBiZjY4MDhmODFjOWU1M2E3NGFkZTZjMDc3NDg1OTcxMGExMWRmNzhlNTlmNDJjNGY4IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImdsbnlOZitFOFdPZENkUEp3OXc2YXc9PSIsInZhbHVlIjoiOHVwMitlY3NmTnF4U3JuRjZKQUNHZU5uWmdpUWVGckR4QVNicUdYSlpHRXpxMk9hNnNhbVpEWnV2ejdCNmdrU1ljb0pCeDRsVHFkdmZobXh0Mk9FdXpycVJPSVljT3Q5cnROQ04wNFR2YWp0YytNTWs2N3JLOHB4Z29GT1VHQ0d5UVpnWkZYTTFWSEhaRnVtYTV4Mm1FMklBdFdsOGN6aVZYVWgva1g5Rmh3blhYcTJLbnlKVy9ITUxpZFZOTjRLRmZOL3JoSkVxLzFNTVE5cVNxZjN3RHF3N2pIVlIvYlNLK1daTmlUT3A0T1FsZGZMVisvMTZUQURIZTQ3SUE2K015ZFRIM2wrenMvRFlTN3UyMW5xbkxhc3pnZlI0ajM0MGhYMXZ5cVdQTTZXbUpvcUNoZ0ptMjU3ajcvVjd0WUJKa2ZwVlQxZEs3azNsT3Z4OHpKTllhaFpKUDA0bzNublRha2dBbTlBcitWMjluREJYNG5KTWNnelJ6c3NmNGtORmtNYXBKcU42YlhZa3VDTEJ0NFJqRlNKQVlEVFU1bUhuUXNWQjVnR2pYS2RmeS96SzNwWXVXcmJCRWhJNCt1SWkvWCttZDFLU045SVhZeHhIMVN3cnBNQnhMZjJubGxheXVtVXZicmF2ZzZtY3IvWGt3Y3NieEpLQ3BMZlEwRVQiLCJtYWMiOiI1OWVkYjY1MWQyZjQ2YjE3MTA5ZDg4NjU4MTU5YzM3YmI3Y2I1NzVkMmExMWQyMTVkNzI3ODUyODY4ZmIyZTFiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjU5RFJIZUtiWmlCRms4NXBGb1FyOGc9PSIsInZhbHVlIjoid0Nuc3h0bEltRGZRV0p5NkdWYWRqOEl6ZG55QVVSL0JhNXRTRWkzUUVuTk5OcWNMNHRmbHZQZHJQUlNDemdtRzZwTTYwbForQk8rVVcycmZGbHdtK1M1eGNGL0trMHAyaHA0bEZaQ0FpN3dMaFRCcUF3NlhIUzZZSGRIa1lmcnlKN3NtLzhQWGtGODIwMjJuQXRMMjhkdzVnTnhubFkvTmJNQS9qTUpJM0VIZzVUaFVwampqaVRmOFpuOTgrL21wLzRXbjd2d2NvQlRMUjJNdlkzTWxaVGU5Ti9GTVhzMk5CNlJiU0E2MlNuMmk3Q1d6MTdNanRablJrMHVsR2VKZXhCelUrTGtFVFVoamw5UGVLN2o4VWNlWkZnOVRzUTBCSTVqbFlMZmwrT2xYbG9zTTlEanhEL2pWLzdveGEyQytObThzRHZCQzhhSHB5dnR1Z3VON25VL21yZ21jZkJkeW9RSDRLN0tySmpWTEV5TFE4Tm9JVllSQ25ucVhuejA0b2xLYnJ1SC9yN1cxM0NBR0x1NS8relMxV2tzZnZmLzVoL0pSd1R1TjdaS0Q5Z1pqajNia3lRRjFEWW9kbGIwWGxGR3B0L2NKaEowYlBLTHluMWt5d2pXbUo3VUI0dkNJNWZpellhNEx0aVJURnhMTDhFQXY1SU1saTR4QUQwdS8iLCJtYWMiOiJkMjkzNDRkNjUxODQyYTBiZjY4MDhmODFjOWU1M2E3NGFkZTZjMDc3NDg1OTcxMGExMWRmNzhlNTlmNDJjNGY4IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImdsbnlOZitFOFdPZENkUEp3OXc2YXc9PSIsInZhbHVlIjoiOHVwMitlY3NmTnF4U3JuRjZKQUNHZU5uWmdpUWVGckR4QVNicUdYSlpHRXpxMk9hNnNhbVpEWnV2ejdCNmdrU1ljb0pCeDRsVHFkdmZobXh0Mk9FdXpycVJPSVljT3Q5cnROQ04wNFR2YWp0YytNTWs2N3JLOHB4Z29GT1VHQ0d5UVpnWkZYTTFWSEhaRnVtYTV4Mm1FMklBdFdsOGN6aVZYVWgva1g5Rmh3blhYcTJLbnlKVy9ITUxpZFZOTjRLRmZOL3JoSkVxLzFNTVE5cVNxZjN3RHF3N2pIVlIvYlNLK1daTmlUT3A0T1FsZGZMVisvMTZUQURIZTQ3SUE2K015ZFRIM2wrenMvRFlTN3UyMW5xbkxhc3pnZlI0ajM0MGhYMXZ5cVdQTTZXbUpvcUNoZ0ptMjU3ajcvVjd0WUJKa2ZwVlQxZEs3azNsT3Z4OHpKTllhaFpKUDA0bzNublRha2dBbTlBcitWMjluREJYNG5KTWNnelJ6c3NmNGtORmtNYXBKcU42YlhZa3VDTEJ0NFJqRlNKQVlEVFU1bUhuUXNWQjVnR2pYS2RmeS96SzNwWXVXcmJCRWhJNCt1SWkvWCttZDFLU045SVhZeHhIMVN3cnBNQnhMZjJubGxheXVtVXZicmF2ZzZtY3IvWGt3Y3NieEpLQ3BMZlEwRVQiLCJtYWMiOiI1OWVkYjY1MWQyZjQ2YjE3MTA5ZDg4NjU4MTU5YzM3YmI3Y2I1NzVkMmExMWQyMTVkNzI3ODUyODY4ZmIyZTFiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962308889\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-234346538 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GdAP3Ala05cLcMv8CjWhgdpBs1UQ8quTxt10NpT1</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234346538\", {\"maxDepth\":0})</script>\n"}}