{"__meta": {"id": "X3e87f32416c5c018c3709fa8a1bb97cb", "datetime": "2025-06-16 09:55:47", "utime": **********.504883, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067745.249552, "end": **********.504933, "duration": 2.2553811073303223, "duration_str": "2.26s", "measures": [{"label": "Booting", "start": 1750067745.249552, "relative_start": 0, "end": **********.190698, "relative_end": **********.190698, "duration": 1.941145896911621, "duration_str": "1.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.190755, "relative_start": 1.9412028789520264, "end": **********.504938, "relative_end": 4.76837158203125e-06, "duration": 0.31418299674987793, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44020152, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.03239, "accumulated_duration_str": "32.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.418699, "duration": 0.03031, "duration_str": "30.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.578}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.467627, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 93.578, "width_percent": 6.422}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 9\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-2069654427 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2069654427\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1910589900 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910589900\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdmZ0czU1cyR283NW9XS2lxZ2lmRHc9PSIsInZhbHVlIjoiOHlsbHY5Nmc2UjRmKzljZm9TMHo2d0NaTVJoTEFyM3FDbnFvcEV6V3ZKQktMUnFYVEhSRDlyOForRGVpUUl2UXZ1eXF0TEFpNEZLR1dRb3g1UEJtUll4RDhFMzA4T2RqRTV5MzZ5Sjd0ZU1hYjhCZHQxK2o2OGF3YUl6c0dia3VIWk1kazJuT054VUZEZml2OGhpYlFLUFphTG1ldk83Vi9YN0FyK083TGFrWE5rU0t3dmQ1dU9nSHpDRFAxSnBlTjBJaUJncDhQMzBLVlllWkxtSkI0MGlCVk56UXJoUEU1VVp5UE13QkxudEVVSk9QRW16cmJTamRXTUlVZ0tSRmJFZTU4UlpQeU9WWWpaNjFURitBaGZqcTMxVnpTNzNHV0R6WXduUzZFZW5kN3U2Vkc4TndTRDN2TEs1UWcySSt3eUhOaVVGM3V2RExIWXR1cUlNYzR1cElHeExUblU5UkptSEdROEFUMXUwejhIRFZkR1R3UjhoL0xkdkxWSlR6dTVqaHRLTS9jUm9ZWk43NU1qYU8zOTJSVkNDOHlBcWNjWllIdnN2NnhleWxKa2hmamF6QzB0S3ZEN0ZNL2hJdS9vZytYczgxUmJ2ckNjR3FRaUl5Nk5sYmd4RXVRL2V1YUtDM1FLRHdyK3Y3djE3YkFmaHNMaHY0ODNzZU5sK2giLCJtYWMiOiJkMzc4YTkxY2RhOGQ2NTcyZjY2N2E5NmJhYWYxNWM1NDIyOGExYWM3YWNkYzNjYzJhMmFiYTk2YTQwYTk2ODAzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJ0N1gvWCtlaUd6SjVvTXd4TG1WaEE9PSIsInZhbHVlIjoiS1F6Y3l5SDh1YXBPelRwUitXRytmYXFvZGhYWnpzb0YrNXJCdWt3RTA3UVBFSGRpWUozMkNua252M2dxY0JVWk5pUWFkV0JOb0hBd0daM0xONk82S2htZlI5Z0NoYjh1ZGU1eDVYU2ZSeE8yL1lZQzcvV0xOWjBPR09HQ1QxQmtPZUNicUdnSnpLVmFnRU1raTVSdFE3Q3hCejVGVENnOUZ1d3p1VW9WVnExUjNMajZOZlRZUW1xVXd4LzV4RERiZE8rWTFnalA3T0F6Yzh4Q0dOMEhWb08rMGpRKzdFQWZ0QnJ4ZFdYVG5XTTNiRUs2OEJ4OU1DV3V1SlFZRHczSHQ3TTl5a2phZk9qdEJPY3lQZ2JFUkhpRnJIbEV2cCtnaStUa0x5eHg1K0w2TmxXK09iODlyYW1xWml5anc4ZjlXUk5nREk0UTdacW5ZdmNUVFRwblFIUHp3WHVrK0ZiV2NhcW5IbTV1TExwTmYvMGpuSC9PUUtIemlVR2VISWlYelF3eTdWZERZRVFLNGJvU0tLMTc3bzdpSTc4ZFRNa092OHhwZW5lVVJiKzFzVW5xT2xYQjZyT3J5U2h2T3FDN3ZncWwzd2FnM1dPUmhab0RhNVFISHVUUHBIbWxISmc2ZUpYNU1FQ1kwVUdHeHYyTXlzRHd5VkFBMmExWUxKTlMiLCJtYWMiOiI4ZDRlMGNmMTY5YzE0NjdlMWQwOWU0NDViMmYwOTg0Nzk1M2FmNGRjNzQ4MGVlYTM0YzA4YTk3YmE1ZGZiMDFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-898478586 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:55:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZoWlBCcWdXOEVWWGZwNTRJdy9TNWc9PSIsInZhbHVlIjoiZG8yNG9aU2RiMm5rUmcrWXpRM0FBYnVRS3RVRjhuMm1vcFBsTUxXV2x1VW8xejFRV1VJbjY4VmlGQ2xrV3hHQ285cWNJL1hyV1gvWXFWclpCS2pqQkVOZEN0ZHdxSlA1MWpyMWtFVlZIYkcveXpna2dWQkR4UVUxZTI3bmQwUzBjbENkWnN1cHdOd0Rmc2pkR0tuZlhNYUh5TEtBRGlyNnh4VHVpaVNCZGFuKzJYY09hU09sVzNMYzM0U2FzV3c3WlE3bGF1WHBiRi8vZGZTdzBkNENZelZWK3JqWnI2ekdGUHhCZmFrdmYxODkxem1UaTUwcFcySGc4OE5QaGhWSUc1ODM1dFNxSVp0b3BsSTI3aHRQQzZuZXBhTmc3TEZ2aEVra0k3TWdpeTdBdUJtbHNjdU50bm90YmdLVkZOMjU1N0tLZU5Pa0V0VHJXNzVTM2IyZDhiNjQ0WlRzUnZtL0kvOUtrY3JSVmRndlIwTWZaeTBJVlp5Z0FWbTdTRWlqZjFXRGJZSHdwWC9nc2NtZElsRUR1VlBEaXFCNWVZTHlFNjFkcUhuUHZBbXkySXJlV3RBU2lFMDVYNk15VzVGNHgyajlXcUd2bDJuSkU3WktDM2h0eEN1TDY0SGFvU0paOHRRelZDODFlcTNoakQ3bzNGMm1vTDErd3NoL0thSFoiLCJtYWMiOiI5ZDk0ZGIwNGM1ODUxYThjYTIzMjVjODJmMmI3Njc4YmFiYTg0NzU0YWUyZjQ0MTJkNTM5NzM3MDA3NzFlZWFiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImREemFvVUd4b3pUbUkxcjJEOXdrSWc9PSIsInZhbHVlIjoidTJzRXJvRTV5aGhRVHMxVElYQ1lZRXlsUGFsWWozWXk4T2tKU0hidjkvTGRQc1poRUJ6MUpVSFJsMmFpV0JmaFJHUWpXUU5kaGpkR01KbDVGYlFKN3dVK1ZFY0FFRGthTmZnTW9YdVBvZHNMdG9ZcHV3bUJGZVo5NW5hS0liSDFvVlZDU1RzMitDVFdyR0FQalowRjkxQWE3MmJtUmJwKzgzZFgxa1EyeUVTQWdxYTg1RU9aYndNaFZRZW9IMnFtazR1a2JabkR5bmtsMmN6bnM0bVFkVXE4dGl6ZjZnUW90TUNyclpXaHhHMTNSRmc2M2FxbytiWjJab0t1RVNZUWJ2a205MWFEa0hjNnJzVzRJdTdMcDVwVnR1NkRYL256M3ZxUGl2YnNPejJicnpzZXp3UFlXdmNGbWJKUVhIQWswWG5VL0lkOXdpOEYvODM5ZTNjRWRLVzJhbjhocmZRRjhKU25CT3UwOHlLazZWalpDcUxGNi8xeC9xNjBCdjU2UnhaNHdGS3ZCbEkrYXdXbDdTUm01S1JuK2lLMTNiNmN1VTlkeG5mdVJVT0NleDBkNExMRFl2RHVwVW1lcmMrNkpQbjkxVUtEd25ZWTJYempEZWdzN1J4Nk5ac3lmd1ljUEhISVczWk5hVlBlWUl3MXRGZFUrV1owMStEbmpCbm8iLCJtYWMiOiJkOWQzM2YzMDRmMjcxYjY4YTE4NjZiMjBiMjc1YzAwOTc4NGMyMjAyY2UxYjIzN2Q1YjgzOWI2YTBiMmY4OTVmIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZoWlBCcWdXOEVWWGZwNTRJdy9TNWc9PSIsInZhbHVlIjoiZG8yNG9aU2RiMm5rUmcrWXpRM0FBYnVRS3RVRjhuMm1vcFBsTUxXV2x1VW8xejFRV1VJbjY4VmlGQ2xrV3hHQ285cWNJL1hyV1gvWXFWclpCS2pqQkVOZEN0ZHdxSlA1MWpyMWtFVlZIYkcveXpna2dWQkR4UVUxZTI3bmQwUzBjbENkWnN1cHdOd0Rmc2pkR0tuZlhNYUh5TEtBRGlyNnh4VHVpaVNCZGFuKzJYY09hU09sVzNMYzM0U2FzV3c3WlE3bGF1WHBiRi8vZGZTdzBkNENZelZWK3JqWnI2ekdGUHhCZmFrdmYxODkxem1UaTUwcFcySGc4OE5QaGhWSUc1ODM1dFNxSVp0b3BsSTI3aHRQQzZuZXBhTmc3TEZ2aEVra0k3TWdpeTdBdUJtbHNjdU50bm90YmdLVkZOMjU1N0tLZU5Pa0V0VHJXNzVTM2IyZDhiNjQ0WlRzUnZtL0kvOUtrY3JSVmRndlIwTWZaeTBJVlp5Z0FWbTdTRWlqZjFXRGJZSHdwWC9nc2NtZElsRUR1VlBEaXFCNWVZTHlFNjFkcUhuUHZBbXkySXJlV3RBU2lFMDVYNk15VzVGNHgyajlXcUd2bDJuSkU3WktDM2h0eEN1TDY0SGFvU0paOHRRelZDODFlcTNoakQ3bzNGMm1vTDErd3NoL0thSFoiLCJtYWMiOiI5ZDk0ZGIwNGM1ODUxYThjYTIzMjVjODJmMmI3Njc4YmFiYTg0NzU0YWUyZjQ0MTJkNTM5NzM3MDA3NzFlZWFiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImREemFvVUd4b3pUbUkxcjJEOXdrSWc9PSIsInZhbHVlIjoidTJzRXJvRTV5aGhRVHMxVElYQ1lZRXlsUGFsWWozWXk4T2tKU0hidjkvTGRQc1poRUJ6MUpVSFJsMmFpV0JmaFJHUWpXUU5kaGpkR01KbDVGYlFKN3dVK1ZFY0FFRGthTmZnTW9YdVBvZHNMdG9ZcHV3bUJGZVo5NW5hS0liSDFvVlZDU1RzMitDVFdyR0FQalowRjkxQWE3MmJtUmJwKzgzZFgxa1EyeUVTQWdxYTg1RU9aYndNaFZRZW9IMnFtazR1a2JabkR5bmtsMmN6bnM0bVFkVXE4dGl6ZjZnUW90TUNyclpXaHhHMTNSRmc2M2FxbytiWjJab0t1RVNZUWJ2a205MWFEa0hjNnJzVzRJdTdMcDVwVnR1NkRYL256M3ZxUGl2YnNPejJicnpzZXp3UFlXdmNGbWJKUVhIQWswWG5VL0lkOXdpOEYvODM5ZTNjRWRLVzJhbjhocmZRRjhKU25CT3UwOHlLazZWalpDcUxGNi8xeC9xNjBCdjU2UnhaNHdGS3ZCbEkrYXdXbDdTUm01S1JuK2lLMTNiNmN1VTlkeG5mdVJVT0NleDBkNExMRFl2RHVwVW1lcmMrNkpQbjkxVUtEd25ZWTJYempEZWdzN1J4Nk5ac3lmd1ljUEhISVczWk5hVlBlWUl3MXRGZFUrV1owMStEbmpCbm8iLCJtYWMiOiJkOWQzM2YzMDRmMjcxYjY4YTE4NjZiMjBiMjc1YzAwOTc4NGMyMjAyY2UxYjIzN2Q1YjgzOWI2YTBiMmY4OTVmIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-898478586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1353295520 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353295520\", {\"maxDepth\":0})</script>\n"}}