{"__meta": {"id": "Xf9238757006ef025c413cbd4d248e5e3", "datetime": "2025-06-16 09:55:42", "utime": **********.57539, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067739.835129, "end": **********.575469, "duration": 2.740339994430542, "duration_str": "2.74s", "measures": [{"label": "Booting", "start": 1750067739.835129, "relative_start": 0, "end": 1750067741.930578, "relative_end": 1750067741.930578, "duration": 2.0954489707946777, "duration_str": "2.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750067741.930608, "relative_start": 2.0954790115356445, "end": **********.575476, "relative_end": 6.9141387939453125e-06, "duration": 0.6448678970336914, "duration_str": "645ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48194920, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1318</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.061180000000000005, "accumulated_duration_str": "61.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1349208, "duration": 0.03023, "duration_str": "30.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 49.412}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.222017, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 49.412, "width_percent": 3.171}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.323544, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 52.583, "width_percent": 4.969}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3403451, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 57.551, "width_percent": 3.383}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.372586, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1236", "source": "app/Http/Controllers/ProductServiceController.php:1236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236", "ajax": false, "filename": "ProductServiceController.php", "line": "1236"}, "connection": "ty", "start_percent": 60.935, "width_percent": 5.443}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3, 5, 6) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3", "5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.392843, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 66.378, "width_percent": 6.571}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.425877, "duration": 0.00315, "duration_str": "3.15ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 72.949, "width_percent": 5.149}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 6 limit 1", "type": "query", "params": [], "bindings": ["8", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.441462, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 78.097, "width_percent": 4.086}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1284}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.458542, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 82.184, "width_percent": 4.544}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.487422, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 86.728, "width_percent": 3.857}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1284}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.501161, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 90.585, "width_percent": 2.86}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.51978, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 93.446, "width_percent": 3.22}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1284}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.533662, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "ty", "start_percent": 96.666, "width_percent": 3.334}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 12, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1307707090 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307707090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.367707, "xdebug_link": null}]}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-518054149 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-518054149\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-286880211 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286880211\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-677389961 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-677389961\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-842865327 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNDckNhYWNIRXZJaXNLM29UU0ZjK3c9PSIsInZhbHVlIjoicDNUQTkwSzZjTXZ1c2hjd3E5YUY0ZjlaYld1WXNjbGtTNUE1ay9FbEw0UDhSOGFMOC9RMmNPRnF2SXlrUHlyYTk4U29WSEl2VXRUYjdZTXQ0Q0xhOUZmRCtYTUhwZjdDdm96ZWpIOG5lbFpBS2Ntc3RoZEV4K2dBWGdWcFR6QW5lMXVyZDVEbS9QbUIyZEdWUThnUXZRRGlSZUY2by8wWWRrT29oZko1UDNwcDRiMVU0N0Y1a3BTWW4zVkZPQmhtYmVvVmZzSncwamxVSWdTdStCUVpuT1pGMC90UlhYY2dwWTlySEJhNEZITDVTK2d0Mk95STBhUlhacWo4N0prNytHVkNweVpZZFpzdnByamNUQTFzQ1BNemJ4aldpMXpEeVdTME9UekRvRzhBamJXYUJaL0ZHSjBiQmppekxLajhvT254MkU3ZEpIRHJ3YjFPZG5KVkt0UWo2a21BNnpia3o5cXNVOW5EYmI1N3JFV3ZWbXprQlk5VUtQdWx4YWNMelpiejMya3FzZHI3bnMzQ3dVNCtiaW5mRTVWcWRYSlVORHAwUkxiQzlGMEdUZE92Nkc0NlMwd3lGTlNFN0kyNmhhcHNwR1BIZnFadWEvNCtFU0g5ZHhvS1pRZkxBMURENEpqSWppbnpCOEM4U2tta2tnQlBNRnZWdEIvcFFvZGwiLCJtYWMiOiIwYzdiZDU3NDYzYTU2NGRkZDQ0ZDE4ZTg2ZTNiYWVjZTNkODI3NWJlOGRlNWM5M2YwZGQxMjQwYjczNmM3ZDdiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InViazc5VXFsaHNDbHNkTjIvenB1SXc9PSIsInZhbHVlIjoibFdmZFRVVGI2dHpUMHFLdHRRcE5CdFdGMlQycnRkUVdOdFJkVkZKK3Z2am8vSDNkaHE5VjlqT2l3aDJaZldld0txS01jSU5DZ0d4azFtUDFocHUyaFROakM5NzJxWkJQeVN4OE5YV055UDhqRllnKzRybytqSTZ3ZXRzZGw4SG5iNHpNMnBITWpPTGxaOWlkdk9PYnN6MU9MamRRQ0w0L25Xd1g5NjNQV1p1ZmN3R1NHSGpZT0J3TnJGUTBvcVBDNUp2dWNFeDFhSjhnS2pGT2Z2eVg0RDdtaUdsbHowaXhQK2ViT2xBSkRnSkxxTTEzbzFETEVTaklDMHdBdzJIMnQzaDZ2OW5qMjZJWEtGaEsva1Z4L2hvZVRrRjRkbTFyYmpWR2lLOVg0TVpRaERaN2E0dEZ1UXBhSWk3WDdFN1Qvd2pmcmNhRTRYVXI2eXBhREtwUUZIcTREcWRpTjBHYzhaamJ5akdMazhOSkNsRmRUbGtVbGllRFMyZytIR2szVHBZUEJPQ3NjRkJ4NVE0a2NUbUMzMTBKTTM3T1kwdkhtYW8rcjNPbEJZVzA5dEhxWHhpNzJXc0Z0OG9oRG5UdHJYOEtZdG43LzNRVGFtQ3J5Qis1aW4yMDdrL29LTEtUYzZ6eTB2SFBMbGd5eWFEQWI4NWFlM3RKQmRselFCY04iLCJtYWMiOiI5NmZhZmFjNGVhZjliMmRmYTg2YjRmMzFlNzkwM2I2NjJiZTM2NjcyMzE5YzJmOTE3NGE2NjQ2ZTRhZWJiMDZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-842865327\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1324934977 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:55:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlgwRVNvK0FzbXVqUlpxelRualJSY2c9PSIsInZhbHVlIjoiQmVUb0RjREwxazVidkhwOTZGbmpmMzI1anhpdkdhTnNpZ2Z6cHBOWUkyNjlFMGxCV0p2ZlNuWW5HQnhGU2VDRXZkdmk0ZUlPa1pkcHlnTFNEWnoyc0kwc2hJN21xb0hmREUwYzQ2dGQ4TTlZQWdRVU1BUmowQlMxS3VpTVN4dnFTc1BodWxwM2djM1NYL01talU4V00wR2VvcjVOWWE2S3p3SURCaUx4eHRFRHdEQlFoNzhGc3dRM0cvNmFqaXdTU29ONG5sSHBPWEtJdjRjc3JBNytLTUdtcFFyandpSHpJWDdhc1BoTEoyeGFIUGk1amMrUkRkQm55TXBVT3ZjWUU3K3ZvUnJ1cnVLWWdZaVN5d2pkZFdOOGd3MDNwZCtxUnNMdkdZTlhrYjFXNVhLc3ZqK0dReFhVeTBjbUh0dEhyZFdHSDV3MUwra3ppQUV2andGZ0pSbFNIbFNKaHNGWTh3YlhKWm03T2pxTnZLN1hDQ2JyS3c1TGhLeS9kbU1ML3R2VmQvZmVFTWh5OEYzMG9ETTd6V1cvSURzUEsydU41aXgxLys4OUtJeDZmMGdSd1VNdGpLMWVJY0k2ZHZOeEZLemVuT2xvNGNYUnd2ZmNaRHpCSVV0THpqYU9wUlU1d0IrRGtjWlZXTVpxNWpOVVRkV2c5YXU1elNNYWx5bDUiLCJtYWMiOiJiMWY0NTFkNzg1ZDQ4NmE2ZTg2MjkzMDAwMWI5YzBkZTVjNmQ5MGU2NjM2ODE2ZjkzMGFhNjczZTlmMGVkNzkwIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjA5WnFGQUg4S0l1UzZpb1hxRGUvQWc9PSIsInZhbHVlIjoicjFJU0djcTRWcWtGYUxDMjEvUmlVS1RjSWExTGFqZGV3czVXZ1dZcjVUZ1pLWGUweHdvZjBNd0FQYjRJVVVGRGNZY0cvc2ZWd3RReTV4eFc3N3JiKzdmejBsSFFSOFhqa2VSajBpdU0vOTVFdWZ6V1ZPVDVVZThnaU43SmlTVGVlV0gwaFBsR0RHaXFEWDQzMkNPVTFJVFliK1FUbXdLNU1NdW94amR4Y0lna2p6MEVTQUF6SkI3Z3VYeXVSZHhiVXozS2F5UWIwRjhZczQyd0JVNmVhRWFaOXB5aGV0dkdhNStUa3huSitITVNuakovd3hLR2lGSWlFbjJycitEU1FSMS9Qc2ZrRkQyU0V3L0ZseU5VbGxLcWdod3h0VzZLYy9wWVhpOW1JbVpucnM3elBIbG45MVdMUDZVVldRSllnaCs5RVZwN1JiYVorbGxOZlZVRFIwQlp4ZFo1V2RRVFQ1S2pEclFxdC9JU095aE91TDFQQ0o4ZldFMW1MQUlYb21ySzk4cjF5cWVrR0xHQUkwUGVpTXVjOVAwcUtSUktQTU9VbDgvU0lKdUxqY3BNOXJCNjc3RnppWERHWTZYc29TZWFoNFo2VjRTNmtROFhFNndpMnRCQ3RkN3JCbFZJaTYzaFpuS3ZYN3JnTzJMSnV1dVRUTzErVndaSnJKeGsiLCJtYWMiOiIwYTQxMmQxNDViNjVhZGQzOWEyMzI3ZTAwZDMwOGVhYTkyYTVkMmEwZDYyNjI4NTljZmRmMzcyNDJhNWZjMTBiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlgwRVNvK0FzbXVqUlpxelRualJSY2c9PSIsInZhbHVlIjoiQmVUb0RjREwxazVidkhwOTZGbmpmMzI1anhpdkdhTnNpZ2Z6cHBOWUkyNjlFMGxCV0p2ZlNuWW5HQnhGU2VDRXZkdmk0ZUlPa1pkcHlnTFNEWnoyc0kwc2hJN21xb0hmREUwYzQ2dGQ4TTlZQWdRVU1BUmowQlMxS3VpTVN4dnFTc1BodWxwM2djM1NYL01talU4V00wR2VvcjVOWWE2S3p3SURCaUx4eHRFRHdEQlFoNzhGc3dRM0cvNmFqaXdTU29ONG5sSHBPWEtJdjRjc3JBNytLTUdtcFFyandpSHpJWDdhc1BoTEoyeGFIUGk1amMrUkRkQm55TXBVT3ZjWUU3K3ZvUnJ1cnVLWWdZaVN5d2pkZFdOOGd3MDNwZCtxUnNMdkdZTlhrYjFXNVhLc3ZqK0dReFhVeTBjbUh0dEhyZFdHSDV3MUwra3ppQUV2andGZ0pSbFNIbFNKaHNGWTh3YlhKWm03T2pxTnZLN1hDQ2JyS3c1TGhLeS9kbU1ML3R2VmQvZmVFTWh5OEYzMG9ETTd6V1cvSURzUEsydU41aXgxLys4OUtJeDZmMGdSd1VNdGpLMWVJY0k2ZHZOeEZLemVuT2xvNGNYUnd2ZmNaRHpCSVV0THpqYU9wUlU1d0IrRGtjWlZXTVpxNWpOVVRkV2c5YXU1elNNYWx5bDUiLCJtYWMiOiJiMWY0NTFkNzg1ZDQ4NmE2ZTg2MjkzMDAwMWI5YzBkZTVjNmQ5MGU2NjM2ODE2ZjkzMGFhNjczZTlmMGVkNzkwIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjA5WnFGQUg4S0l1UzZpb1hxRGUvQWc9PSIsInZhbHVlIjoicjFJU0djcTRWcWtGYUxDMjEvUmlVS1RjSWExTGFqZGV3czVXZ1dZcjVUZ1pLWGUweHdvZjBNd0FQYjRJVVVGRGNZY0cvc2ZWd3RReTV4eFc3N3JiKzdmejBsSFFSOFhqa2VSajBpdU0vOTVFdWZ6V1ZPVDVVZThnaU43SmlTVGVlV0gwaFBsR0RHaXFEWDQzMkNPVTFJVFliK1FUbXdLNU1NdW94amR4Y0lna2p6MEVTQUF6SkI3Z3VYeXVSZHhiVXozS2F5UWIwRjhZczQyd0JVNmVhRWFaOXB5aGV0dkdhNStUa3huSitITVNuakovd3hLR2lGSWlFbjJycitEU1FSMS9Qc2ZrRkQyU0V3L0ZseU5VbGxLcWdod3h0VzZLYy9wWVhpOW1JbVpucnM3elBIbG45MVdMUDZVVldRSllnaCs5RVZwN1JiYVorbGxOZlZVRFIwQlp4ZFo1V2RRVFQ1S2pEclFxdC9JU095aE91TDFQQ0o4ZldFMW1MQUlYb21ySzk4cjF5cWVrR0xHQUkwUGVpTXVjOVAwcUtSUktQTU9VbDgvU0lKdUxqY3BNOXJCNjc3RnppWERHWTZYc29TZWFoNFo2VjRTNmtROFhFNndpMnRCQ3RkN3JCbFZJaTYzaFpuS3ZYN3JnTzJMSnV1dVRUTzErVndaSnJKeGsiLCJtYWMiOiIwYTQxMmQxNDViNjVhZGQzOWEyMzI3ZTAwZDMwOGVhYTkyYTVkMmEwZDYyNjI4NTljZmRmMzcyNDJhNWZjMTBiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324934977\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-122461418 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-122461418\", {\"maxDepth\":0})</script>\n"}}