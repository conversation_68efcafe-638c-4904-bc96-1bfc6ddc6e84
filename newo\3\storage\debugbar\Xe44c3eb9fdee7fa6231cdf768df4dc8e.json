{"__meta": {"id": "Xe44c3eb9fdee7fa6231cdf768df4dc8e", "datetime": "2025-06-16 09:41:36", "utime": **********.690377, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750066894.571326, "end": **********.69043, "duration": 2.1191039085388184, "duration_str": "2.12s", "measures": [{"label": "Booting", "start": 1750066894.571326, "relative_start": 0, "end": **********.37277, "relative_end": **********.37277, "duration": 1.8014440536499023, "duration_str": "1.8s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.372796, "relative_start": 1.8014700412750244, "end": **********.690435, "relative_end": 5.0067901611328125e-06, "duration": 0.3176388740539551, "duration_str": "318ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01419, "accumulated_duration_str": "14.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.547698, "duration": 0.009349999999999999, "duration_str": "9.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.891}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.604504, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.891, "width_percent": 13.39}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.639619, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.281, "width_percent": 20.719}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-310867291 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-310867291\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1383902757 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1383902757\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1456543275 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1456543275\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1827541589 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=116a2vl%7C1750066891256%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpxM1BrSmZmcC9KYlRkV1ZtbFg4alE9PSIsInZhbHVlIjoiQ0thc21jVUhBS0pXUWlmaG9mWnZDbzEwaXl4RjgwTEM2U25HRmN4MTVZeisvMmR0MFQ4eXlYNlNBTkhSbmdOa1R1Mytqb0VpYy9iNG0zei9XSVRZQk1SVmRwUHdKM2NQeEhhQjNMRXBJOWZ1RXFpTnhTcERMZUpUT1hUbXh0QXdKdDFvTkFEbjVqQTV0bzdYeC9xdjg3aW53eHd3RCt5QmZWYlVSUm82M1ArZmRyTCtDbWRYOUhaODRoY1VCbElqYUsvZC9xSmxhNWdIRFFjSVlzY2dWSTZzVmRYUHhJMnNWdG4yYzk0cnlua3N2V1N2T0JDYUtabUJCaFh4c2RiZzd6VWowemM1cmlGcU84akJteFFwRWkrajdaT1Vya2RvZnVDOWdrdVlEQ0tzUUQ0Nzd1Y3ptdHlpNHBVaXdxWVQ0cEdRblUvMDNxU2RiRU5oSWlKaDFIaUordlNOTUYzK0xrRzhHejFLcFRXSGdVY05ZQkpYdTBPQnRuZlE4RHNSYjNhN1F1aHBrVDhHTDJ6WmFhNE1HSDBVVWwxTG9zcjgxSElHVGVMdUJObVBydlNmVXowQWJVMEV6OFlKcmhlVHE5VDBjNkl0QWY0V3VCODRBSjhCL3lOSjloTDB2V29SUkU5a2EyRGUyQnROb01EMGcrMVhXVzI5WTdoR3NXOVQiLCJtYWMiOiIyNDhiZjNhMzQzZTBmOWY1Y2U4NmRmZDJjZjk4ODk1YzM3MDY3NDc4ZmM3NTZjN2NkZTZkNjIxYjZkYTJlNjA5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik5RMUZ2T0JQWmtyVE85alNZUXJOa1E9PSIsInZhbHVlIjoiRXA0SGtoNFpJSlhGZ2VNS3NUeExlQlI4eFpnQml1clNlbnU1OWxnZ2pFSG9kaXFPMStCWTA0T1krSjlaZUE4eVMwQ1paYWlGVmUwV2dzSlpsRVNrb0VHQjEyQUhVdHJ3RFFOTnJ5UlNGMWZScEpUVmlFYUl5RVQvcitGNUxqditmc2RRUUlJbzNaY1l4elFmd2RxdG9xZG5paS9YMUhIdHR4YmNmZzBHenBNbC92eDB6NitpbjV3NnVkNERVMGl6VXBObFczUW83aGY5RmlaMkZWNytiYklIbGY4RFVHUzNxQmN6enF5SkNRVTFHaUkrUHByeWpCMXZMeUxIV0VXUnBPV1FpcU9BNzAvWHJtd2VSR2UreXFodURwU2ExR08rYS8zYVA4TkxjbHovcGxueVRFRW9HL3dZUWxacDdnWnNPWTBKbjdsa0w4WXpsUkMxUC8vZkhpVXZmNHMzbkhvQ3pGbVZZdSs1eHdyQTNNY0dzN1p1d0pMMHNIQ0N0d2hVV3EwSnJSeUw0VzdwcW5QblBnNHhFYndrU0lUOW1manNHY2pGZk1Gb2JROGFDYmJrVEhSdmZ4ZUlVNkl4STl3blp6bStZU2o5elFnMnlTcnBzTEpJRVZ5bkZyLzNsK2NjLzNYVk9lSlgyN0dWMEIzakZqcmFKcVpiMFdEWm1wWWEiLCJtYWMiOiJjMjk2MzM1YmQwYmY0NWU0ODljZTBlN2I5Zjg1N2Y5YjA5YTJmYmZiMTU5Njg3NTY4OGI4YWYyOGViNGUxNmJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827541589\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-472839039 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472839039\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-542159457 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:41:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InQ4RDV6TEdWQlpjTy9sR0pxWkRJMEE9PSIsInZhbHVlIjoiWXNqclpFay9VZEdlUlM0N2EybFRLWEZQR2w3TXVKcFIrL1EvSWxxSmFHcldGRlRuRitKSmpOWFJsZWpmREhtQjZnRkQ3WHUrMWt6R3ZzQ0lWVUhzQWRreTV0VmQvRnNPUWhxRnhsYlZPN2V1eUk5U0FuU1YxSzJ6amYzNE5MRDZuNFgwdlE4bWtydW9iZk5XWGhCT3liTkdCNDlBRldKL2VXeVJjUUh3Qm0wbDlmUGwvMk4rbzdtbmVjc0d4d0lrdmxETkNVRFcvMDRGNUJCYkZ6UVRFcUp3b3NGU3ovOFhteGpzSS9tR0t3d2NZay9XN0FuMjVLSHFlU2lteW41OTNMMmtCSUNXU1VXeFVsR05VNU81SDNFUk5UWW1qVjNXY1ZZVWQ0NTNWbUNwRmhkMG9sbSt5YzZrQU9vM0lsZVZ0YmdFUVV5MWl1MFA0UDByVkNaNUlpRTJaZytvWHY3dWJqUGJNTkdva2phOTMzTlppeVhvUDJoUXl6TU8zNmpQWll3T1pwNEJYdFBpNUlDc0Y2Uk1oU0pxMklTU3VEYkowWGc3QS9CSGNBT2NNUUdnbXpXRW5EU3JLZk51dWNPeGczZFRzNWg1WGVkdDFXRmR6SzFnOGRBNWdCUGU5Qk1vMmFvM0ZWa1FlOUY3YzFVZXo2dDEwY1Y0TmkyVDkzNmciLCJtYWMiOiJkMDU0YWMyZjcyYjA2NDIyYTM1MDk1OWEwZGEzZjZkNjllNTMzOTQ1MDZkNWM5NDdiNjk1Mjk0N2ZmZWQ2YzliIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlaOWpNQXFFaVBwUklmemY1aS9mdEE9PSIsInZhbHVlIjoiend0a1NaQWtGYmdRSDl1Qk1RNGk3M0hLY08xcVdRcXV4Z2o0aXpFVmE4OVNkTDhGS2doSnpaTUJxUnB3ZXFlaTdhcnpYdURXeWVxVHRPU2xiZzVaa3BYdVFOQU9TbXNzY1JJdFNyWUNETTQ5ZTNPOHpmQ1JIQnBWU25RYkovM0ppSlUvRytjSFNob0czS1hINmc5RFd2MHBQeGdtRWFkS2NaZFBCNk1GeXZ1cVJlRHFNa09VUUJkWmZPa1MrTEdJUnpPNEYrYjY2b3ZJODlWQlNVYmRWYTVNbmd0Z205Q2p1NGVDVGk4ZHlDSE5iaytMMGs1T0ZBY1g5RERNY29scWIzL0tDeXFjZlpSbndkVEZLU1o5L2NEbFdRMWtmZ2d4ODdFSjFZaCtqOElHT090NEh3ZERndGJvcTNTWTZDdjZKSDlJYlV5M2l3a2orYnVHWTV3MTR2ZE9BbGF3ejNIejQrcXpGUnppeThNQnpkRjNQa1lEWlZ0eXNuKy9yN0VwNCs1VnRmNk5mR2hBZXNRS2JyelBHWFM1dHk1ZkJyUkpFQVdQK2RxSlVGWlRiVHFBT3lCTXVkUVJRWjk5YWlIYTBIaXpndkhQZzY1VmpBbGlDRXI0MEJTOU9UdFV1aTZKRTNzVFZWNllvNll5ODVKa3I4UGJ1VEpMdlR0NE0yaEUiLCJtYWMiOiIyMzVlNTk0MDI0OWY0OTk1OGI1ZjQyMWQ2MGZhMWJlYTRmZDQ4ODcyZTc1ZGFmNmNlZjZlMDMwNjg4ZTMwOGU1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InQ4RDV6TEdWQlpjTy9sR0pxWkRJMEE9PSIsInZhbHVlIjoiWXNqclpFay9VZEdlUlM0N2EybFRLWEZQR2w3TXVKcFIrL1EvSWxxSmFHcldGRlRuRitKSmpOWFJsZWpmREhtQjZnRkQ3WHUrMWt6R3ZzQ0lWVUhzQWRreTV0VmQvRnNPUWhxRnhsYlZPN2V1eUk5U0FuU1YxSzJ6amYzNE5MRDZuNFgwdlE4bWtydW9iZk5XWGhCT3liTkdCNDlBRldKL2VXeVJjUUh3Qm0wbDlmUGwvMk4rbzdtbmVjc0d4d0lrdmxETkNVRFcvMDRGNUJCYkZ6UVRFcUp3b3NGU3ovOFhteGpzSS9tR0t3d2NZay9XN0FuMjVLSHFlU2lteW41OTNMMmtCSUNXU1VXeFVsR05VNU81SDNFUk5UWW1qVjNXY1ZZVWQ0NTNWbUNwRmhkMG9sbSt5YzZrQU9vM0lsZVZ0YmdFUVV5MWl1MFA0UDByVkNaNUlpRTJaZytvWHY3dWJqUGJNTkdva2phOTMzTlppeVhvUDJoUXl6TU8zNmpQWll3T1pwNEJYdFBpNUlDc0Y2Uk1oU0pxMklTU3VEYkowWGc3QS9CSGNBT2NNUUdnbXpXRW5EU3JLZk51dWNPeGczZFRzNWg1WGVkdDFXRmR6SzFnOGRBNWdCUGU5Qk1vMmFvM0ZWa1FlOUY3YzFVZXo2dDEwY1Y0TmkyVDkzNmciLCJtYWMiOiJkMDU0YWMyZjcyYjA2NDIyYTM1MDk1OWEwZGEzZjZkNjllNTMzOTQ1MDZkNWM5NDdiNjk1Mjk0N2ZmZWQ2YzliIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlaOWpNQXFFaVBwUklmemY1aS9mdEE9PSIsInZhbHVlIjoiend0a1NaQWtGYmdRSDl1Qk1RNGk3M0hLY08xcVdRcXV4Z2o0aXpFVmE4OVNkTDhGS2doSnpaTUJxUnB3ZXFlaTdhcnpYdURXeWVxVHRPU2xiZzVaa3BYdVFOQU9TbXNzY1JJdFNyWUNETTQ5ZTNPOHpmQ1JIQnBWU25RYkovM0ppSlUvRytjSFNob0czS1hINmc5RFd2MHBQeGdtRWFkS2NaZFBCNk1GeXZ1cVJlRHFNa09VUUJkWmZPa1MrTEdJUnpPNEYrYjY2b3ZJODlWQlNVYmRWYTVNbmd0Z205Q2p1NGVDVGk4ZHlDSE5iaytMMGs1T0ZBY1g5RERNY29scWIzL0tDeXFjZlpSbndkVEZLU1o5L2NEbFdRMWtmZ2d4ODdFSjFZaCtqOElHT090NEh3ZERndGJvcTNTWTZDdjZKSDlJYlV5M2l3a2orYnVHWTV3MTR2ZE9BbGF3ejNIejQrcXpGUnppeThNQnpkRjNQa1lEWlZ0eXNuKy9yN0VwNCs1VnRmNk5mR2hBZXNRS2JyelBHWFM1dHk1ZkJyUkpFQVdQK2RxSlVGWlRiVHFBT3lCTXVkUVJRWjk5YWlIYTBIaXpndkhQZzY1VmpBbGlDRXI0MEJTOU9UdFV1aTZKRTNzVFZWNllvNll5ODVKa3I4UGJ1VEpMdlR0NE0yaEUiLCJtYWMiOiIyMzVlNTk0MDI0OWY0OTk1OGI1ZjQyMWQ2MGZhMWJlYTRmZDQ4ODcyZTc1ZGFmNmNlZjZlMDMwNjg4ZTMwOGU1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542159457\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-582698241 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582698241\", {\"maxDepth\":0})</script>\n"}}