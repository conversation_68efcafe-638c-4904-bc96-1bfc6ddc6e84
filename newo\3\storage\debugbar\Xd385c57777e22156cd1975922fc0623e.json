{"__meta": {"id": "Xd385c57777e22156cd1975922fc0623e", "datetime": "2025-06-16 08:50:45", "utime": **********.555548, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063844.144568, "end": **********.555579, "duration": 1.411010980606079, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1750063844.144568, "relative_start": 0, "end": **********.330051, "relative_end": **********.330051, "duration": 1.1854829788208008, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.330074, "relative_start": 1.1855061054229736, "end": **********.55559, "relative_end": 1.0967254638671875e-05, "duration": 0.22551584243774414, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45209856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.029099999999999997, "accumulated_duration_str": "29.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414307, "duration": 0.023309999999999997, "duration_str": "23.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.103}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.464233, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.103, "width_percent": 5.979}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.515581, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 86.082, "width_percent": 7.835}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.534529, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.918, "width_percent": 6.082}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-869489212 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-869489212\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1819758496 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1819758496\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1459479255 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459479255\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-266670310 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063832651%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVHWGFtbGNmd2tIaWF4ek5mNG5hOWc9PSIsInZhbHVlIjoiOG5vMU5FQnBTTy9TMVFlOFJXN1FuYkNodmhNZHlRRUJ4UHpTS0E4NEFrZVBaNHN5SzJyUVVRM3lzaWwxSlBOQ1BldWJQVi9PS1dIS3dVNFZjR2dreHNFa0h4NE8rcXVNbHRZcFRWUDB4QTN0WDZrSmg4NGQ5Y2VMeCtxOXduU3Rlc1RWT1JocGl5cVBmMGkwYnQyZEdLR1RSOS9NSEJHZjY4K0xNRERBUjZQZVZLR0lrMlRES2Y4a0ttU2tvVzRCQ1EyZVQrSktKRERMSlRqOEFTcXhrcTJtN0x4c0ZHU3ErTmVwL1ZhdDE5am9uV2M5ODRheXRKaURQanNrMU1BaWxsMzQvU0FCS0VHUmNPU0dGT0RTRUg1SG1OVlZEVGx2Z095VEZHYktTZjU3L3lKNHArMGFZM3NaUmJ2d2h3VW44czJaeXo4Q0xzQXdOckpwYjNhb3VaMUQzRDRoNmR1MVEva1NSNmFybXhPRURISEdUY1FtRFVjM1dhS0JkSUxSRi9NRjJ2ZVJXQVZKOXZ0UDFLZmFOajdES2dud0UrVGI5UjV3aFRSNFFEQmQ5L1RtMXc4OGtpTXRIekJjOHJLRU9ham1MeFQ0cFdIUVhjRHZWRkR4QTZDcENrK2hUNDRKY0xGb2twYkRsTEowOUh6SWRuUkpvQnpLWFhhUllTVHYiLCJtYWMiOiI0YWViYzFjYjQxNWEzMTY5MjI2N2M0MWMwZGI1YThlMDI5MzY3N2UzMGZhOWVhODQxODczZDNmOTFlMmVmMTYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdoOXQ0V2wvZGFubnNFbjNtd1RsRWc9PSIsInZhbHVlIjoiaXZMNzZDTG40Ukp0Z095ajlPNyt3QWVsWmFRcTZMTUFqQjl1Nm9SMUVTZjlyNVlCRWlKZjZ6THJ0ZFFuZ0xEYUdFZEdob2pNc1NER3dmWDZEYnYzS0FBTFJPcHdONXhvWHY2cy9Dd1Q5aGtsZWxSNU9JN2VRNFdPaHN6aWFTSnZudkI3cjkrcEFXeXJhendIR05KTzQxcU5GQVEyVHNWaUdMRHhIMWl5eXhtc1lvMkVKM3dQL0Njank5ZnFxcXZpN0ptVEpFa3lXT3l5Qkl1TzNMeDVPSHhFenppRUFzYi9LbmZkTncySFl5aUo0aUQxNTlVL1Q2R3lFcFNkNHc0Sm1US25PU1RzWlQrOGF4RHc3WWZMRHFRakVldURXeVlKSVNWN1ZPS25oNkVUdUtTN0ozaE1RMThKQzBXZHdBZjhEZEpRaHZ2OUlyWmtXRXd0c1ZNZ2RnMVRGR2RRTnFYTFVWOG44ZTRKYm5BUEZycEJiRytMMmY5bkRBYTgvL29XTTBURDFTUlRJYmhJdm8zVGsrUFZ3VkZEbXdsZ1AzdjAvYU9GNys3dWMycDNId3ZXbHZmQnJIWVJCdGFuZ0lHazJObnFXMjl2TjdvdCtlSWJ6TWpQNlUxaG5HTWowZDg5SVZ6UmZQYU9EUWVXYVhkYlk2MEp2NEFlZzhtSE04aUYiLCJtYWMiOiJiODllZGMwOTUyNjcwMWMwN2Y3ZDgxNTFmOGIzMmIwYTJmNDYzYmQ3M2Q0NGI0ZTIwOWZjYTEyM2QwYTY1OWRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266670310\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1602603288 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xy8o1OLZEDaYyV6WX9XzzYzbw5zHFfm46SvuTNQ8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602603288\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-236041234 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNVdzZCWDNUaVJVanZYd3NRalVVc1E9PSIsInZhbHVlIjoiWlpDL0MweEdCMjh0WFcwZzJpTDdXTk1RQWJkSVpxNzBSOXM3ZU5IeXN5UE10RzZ3ejkrWnVMQzBGS1JiekQ5R0hPWTNaU0liSXl5M2JxS213Y3hFamFSUlhtaVU2YlZuNmlHTlFVcTdDRDFwaFNnZFhDblQ0anA1VE1FbGZRZ3lmS3N1Z1BTS0kzUG5DQmpMWWt1ekd4UHN1cktXTnJyaU9Pa1VWWEJXdkVXWmViSnh5L2s3amRreGlaKzlCRmt6V0RiNlNadjJHdk96d3M1Qk82MnFHM3oxUmZHL0NoR1lGTk9DbVlQYmdwekhJSWs3NCtUMjEwM2NQQlpKOE9UdUpNTExLMkxXc20vMW51QW9LVFlyalV1Nm1iTDVnWlVsekRZSEcwNUFsQ21CTHBoblJPSVpIV2RsbEc1RERhaVpHMmZxWFRPM1NlVHBoY2tRNndQNW9QTys3TFd1Vk51S1VHNmZzSFdaQlFXa3hsN045Sy9kZ3QyTk53L3lVVmdsZG9CZFgwYjJIUTFnUlc3WE9vUjVCYkhHR3QxcjI0MjJYL1RGUytJQmZmRE9abWFTOEdGZlp2dGo0bVdTNXAySmh4NnZhaTN6Nm5MdVVlZFRLNXhSMGhza0E4V05JWUxYK1pYQS9NYnlqUTBBL1J4NStJY3JDRllTeWFhRW1qYzYiLCJtYWMiOiJkYWRiODU0NDk5YWIwZmMyZjJlYmMyZjUyMTczY2E0YmIzMGY5MzJhMjU2MmM4MTA3ODE2ZWFhNTRhMWE5YzFhIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpjY2poZ2daR28vRlBsQkE5NWRmQlE9PSIsInZhbHVlIjoicU05Qkw4VnBRQ1RsOGhhYzFEM2k4TWh4SGk1MDl5YzYwZHgrd0dEa2plaEcrZE1XTDBMZU1hQlppUG5CMnNpc3Q3ZFIwRFhBYVFEeHFVTEZ6alBKd21WWmMwR243K21ZR1NzeHAvckNBb2RCQ1p6WjFrQ2xnRnh1SWxKaVJwZldtNno3WkxpSFJVekQ4QW1yMlNsSFJUMUV6NEhWdWhZa0FJZDVqdXlsZU1rRHRjZEpDam5INldOdlRtREV2aEZPRXlqd29rVEdUb3Vxd3FkVkptWWlHWHZ4ZEUvQm5vMFVWdDVBK3RVZG1LbXJVSUtTMlB3QnNFK2NuTDVDcUJ2UkprWmlNSGpvK3VhNzQzVXE1QkdYUjdkc1VUenVpcHduOUhYeXdUQ3QxaXRTTldJWHFNUnpPK2JOS28rbFFNOTdqTDBCTjZKQlFaSzY3T2dOM3VtUGJpZTI4aFNpanJzWUZ1TFFaaTRlOHBmNk8rQUs1VWNaemdTT0NhcDhENTY0cWxaYjI4QzNpVUNDUW1NS3RFTEFRRCtxTHFnTGRRRVZlRmJGb1k0Y1BXMXVMdjhwVWM5YWNpZEVlT3JEdmsvTHpHZXRsUm5pNkNITnc1YlZiSkZrQmNNeEFEb1ZtN1RCVWIxMW5hZWMvTmlXcE14a2xpc1pQdjB2b0paZUJCanIiLCJtYWMiOiI4YThlNzVlMGJlMDg1ODg2OWJhZDYzMDkzOWM5YzdlZjE2ZGE0YjcxMjJmMzUyZTk3YzEwMmM1ZmU3MTZmN2JlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNVdzZCWDNUaVJVanZYd3NRalVVc1E9PSIsInZhbHVlIjoiWlpDL0MweEdCMjh0WFcwZzJpTDdXTk1RQWJkSVpxNzBSOXM3ZU5IeXN5UE10RzZ3ejkrWnVMQzBGS1JiekQ5R0hPWTNaU0liSXl5M2JxS213Y3hFamFSUlhtaVU2YlZuNmlHTlFVcTdDRDFwaFNnZFhDblQ0anA1VE1FbGZRZ3lmS3N1Z1BTS0kzUG5DQmpMWWt1ekd4UHN1cktXTnJyaU9Pa1VWWEJXdkVXWmViSnh5L2s3amRreGlaKzlCRmt6V0RiNlNadjJHdk96d3M1Qk82MnFHM3oxUmZHL0NoR1lGTk9DbVlQYmdwekhJSWs3NCtUMjEwM2NQQlpKOE9UdUpNTExLMkxXc20vMW51QW9LVFlyalV1Nm1iTDVnWlVsekRZSEcwNUFsQ21CTHBoblJPSVpIV2RsbEc1RERhaVpHMmZxWFRPM1NlVHBoY2tRNndQNW9QTys3TFd1Vk51S1VHNmZzSFdaQlFXa3hsN045Sy9kZ3QyTk53L3lVVmdsZG9CZFgwYjJIUTFnUlc3WE9vUjVCYkhHR3QxcjI0MjJYL1RGUytJQmZmRE9abWFTOEdGZlp2dGo0bVdTNXAySmh4NnZhaTN6Nm5MdVVlZFRLNXhSMGhza0E4V05JWUxYK1pYQS9NYnlqUTBBL1J4NStJY3JDRllTeWFhRW1qYzYiLCJtYWMiOiJkYWRiODU0NDk5YWIwZmMyZjJlYmMyZjUyMTczY2E0YmIzMGY5MzJhMjU2MmM4MTA3ODE2ZWFhNTRhMWE5YzFhIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpjY2poZ2daR28vRlBsQkE5NWRmQlE9PSIsInZhbHVlIjoicU05Qkw4VnBRQ1RsOGhhYzFEM2k4TWh4SGk1MDl5YzYwZHgrd0dEa2plaEcrZE1XTDBMZU1hQlppUG5CMnNpc3Q3ZFIwRFhBYVFEeHFVTEZ6alBKd21WWmMwR243K21ZR1NzeHAvckNBb2RCQ1p6WjFrQ2xnRnh1SWxKaVJwZldtNno3WkxpSFJVekQ4QW1yMlNsSFJUMUV6NEhWdWhZa0FJZDVqdXlsZU1rRHRjZEpDam5INldOdlRtREV2aEZPRXlqd29rVEdUb3Vxd3FkVkptWWlHWHZ4ZEUvQm5vMFVWdDVBK3RVZG1LbXJVSUtTMlB3QnNFK2NuTDVDcUJ2UkprWmlNSGpvK3VhNzQzVXE1QkdYUjdkc1VUenVpcHduOUhYeXdUQ3QxaXRTTldJWHFNUnpPK2JOS28rbFFNOTdqTDBCTjZKQlFaSzY3T2dOM3VtUGJpZTI4aFNpanJzWUZ1TFFaaTRlOHBmNk8rQUs1VWNaemdTT0NhcDhENTY0cWxaYjI4QzNpVUNDUW1NS3RFTEFRRCtxTHFnTGRRRVZlRmJGb1k0Y1BXMXVMdjhwVWM5YWNpZEVlT3JEdmsvTHpHZXRsUm5pNkNITnc1YlZiSkZrQmNNeEFEb1ZtN1RCVWIxMW5hZWMvTmlXcE14a2xpc1pQdjB2b0paZUJCanIiLCJtYWMiOiI4YThlNzVlMGJlMDg1ODg2OWJhZDYzMDkzOWM5YzdlZjE2ZGE0YjcxMjJmMzUyZTk3YzEwMmM1ZmU3MTZmN2JlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236041234\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1756217702 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756217702\", {\"maxDepth\":0})</script>\n"}}