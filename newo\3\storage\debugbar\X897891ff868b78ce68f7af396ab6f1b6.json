{"__meta": {"id": "X897891ff868b78ce68f7af396ab6f1b6", "datetime": "2025-06-16 09:53:49", "utime": **********.126761, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067627.324775, "end": **********.126789, "duration": 1.8020141124725342, "duration_str": "1.8s", "measures": [{"label": "Booting", "start": 1750067627.324775, "relative_start": 0, "end": 1750067628.909908, "relative_end": 1750067628.909908, "duration": 1.5851330757141113, "duration_str": "1.59s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750067628.90993, "relative_start": 1.5851550102233887, "end": **********.126792, "relative_end": 2.86102294921875e-06, "duration": 0.21686196327209473, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45149440, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026930000000000003, "accumulated_duration_str": "26.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.012345, "duration": 0.024050000000000002, "duration_str": "24.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.306}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.070973, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.306, "width_percent": 5.273}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.095499, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.579, "width_percent": 5.421}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2124868136 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2124868136\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1676967017 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1676967017\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1027588373 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027588373\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1552772394 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=116a2vl%7C1750067612209%7C4%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpzT3hlT1AzdlczTzQzdERhNzFvaVE9PSIsInZhbHVlIjoiV2pSV1pPaTBtUE1yYmN0VXpENngwOXh2WlpSVlFUbE9hS1pQVnZ3ZEVrZ2dSWGdWaCszM2ttT0hrMDkyVkxOVzZsaDNhSkJ4VXp4WGk2Vk9nUWtqZGcrd3NNRjBDQUhITmM0TVpWM2ZVSmY1dnJ5dU9Mczh3bXp5VC9lTUd1bDRlTlN0dG5KWWgyK2UvQ0hPSUQ4dHdoeEN6SFNJdEg3VkhWMlZGZTUrQWFKS29rVkdZZU5vTVQ1QTRQZlZvV3I5L1p5SlFSMHQ3Q0tWai9OTmVRWmFySjVMVUxlOFI2Nm5rTm1IWEQrRDFHdlZlSjJqWFVJUkFWVk93aGJMZVhJUDZpSW1aaUdSa2RiS0Q1SjVlR2Z0RWNYMzgzS0N2Q2RkbGFPa1V6K0RKNVlMdFF6b1pTYzFzTkFJRkFNeDI5bkJuUnJ2STF0NUZGNjNhRkpDdy9CUkN0UXZRUWo3eDlXL0d5bFRydWtCdlhBZ2JKTE9XNFNEUGNPWnorSHlZNCswdC83SkZQRS9ZeWRDejlVanJGVjFSNndiSW90R3doQ0d6YkNrb1J6K1RoZ1JzdDNsbUVtYVRNVnd2eGpySmM1TWRIa1hITmYvRTlEbkhZR09VdmVOWFRZcVJQaGQrZ00rMWdmWXhHNEdlSGM2SzB0TFY1MEE3RFlxa25LTGdHU2oiLCJtYWMiOiJlZGI3YmMyODU1OTU5ZjBlOTk4ZDQ5OTRiMzJiZjE2YjU2OWI4NjVhMGY3YmMwZGE5YTdlMjRjYWY4MGM5YjE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlMzZlc4KzEzdDJrdmZ4ZXVZUUVPWkE9PSIsInZhbHVlIjoiQVZWTnVWNzhibmU2YzhNaXFrZm1KLzRoYStrSlJCdGg4MGFyTmkrQjcwSjdyZC9xNTBpQnRhTTYvRVlIbFJ4SmI4WWtHektaVklUbGFCd25GaUppRGdKenZzVlFkWXRoVDN0Vm9kWWNlNHI3dCtBZmRMbjE2ZUgvZ1JVTUVVQjROTHlRd0E0VkRHa3NKZzczR3FyOVpGVk9nNCtablZwYkN4czcvRmRuNzAvblR3UFZFSnZyOXJEcTh5bDY5NjZ6a2xNWUpMQ0NmZ1hpamlQQlBhKzZkc2xwR2hxYytQaWJkS0o4akx3TDJsNXVQdEI1K2VHcExZVnhxbHlqczhkWGJBSEdVMU5rYVBBRklNcFpNMjgrRDF3OVE4Z0hJREZIa3pId08xS2lVYVVBMDd3bFU0NmlWMXEzb0RRWS94QVpoQm5pc3pVRWZMZ1lrZTdjMUVJYmVtTUhGWjFLd29jVDl1SXhRRGhRZHZaeWd3SlJpcUUzM0hYQUY4QVp6Q2ZZVWZDa0p1NE9vbzYrcUlqemwvTHhzQVVsQVBSUHVJOEJBSVhCcW8zdnpwU1N5SEc3T1pNUlA1bEMvSlhaUlJtMkIrMlhnei8rMm9ES1E0QW1nRjlSNUNhYXVxNko4aFQ5WDJGVkxiUDRHSzR5Wk1mUktMRVB4NGdlQ2RIU0pYclEiLCJtYWMiOiIyZjRlYTdlMWJiNGM3MDYxM2ZjNTQyMDA4MGJkOTM3YzgzZGJhNmI2ZTUzMzQwZjdiNWRlMjE0OGE2ZjY1ZjViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552772394\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-604617361 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604617361\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1603571376 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:53:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9HNDY5cU9JYWxxeTZLR1UvUXJkb2c9PSIsInZhbHVlIjoiRm5FNWdEVDJwWlQ1eTRZb3ZOL3BZVTFRRmZUUlZCWFk0Q0ZWcFgxSGsxUXVOYlRmN011clhIdGFWOXI1d2I2c0ZGQnp2YURyOVZaeEZOdWJmZnBUMnBSR2w1YjJTMFZyNm9ZTklSVzVRSFYwUUoyTmRsN3d4bjNXUW1GZXhCc3dWcHprcGdJWWlxWmFhb0FRUG15Y3ZiUXFaZDdqSTlNVlg1OWhOVjVuTTBTU1NtM2hKVVBXYW9hUy9Fb3lXWFNFdGo4QVVtMUVibll3cWlUZjlEdTVHVzQ2Mmp2Tm0vY0cyNzkwWmNpaitxRmVBUFI1YWl2VHJoWFloaFJKSnZiT2tNTWQ4TnRWTDZ0WHNwd2dCLzNOUTlxUnJwNTkwRlNMeUIvalV5K0JmSzh0cVRYSkZGcWdTb3orOUwrUWJTRnBWSm9ISUoxcDBRZGQrQkxKbS9WRlVMY2JjaDN2R3pzYVF3TjV0VHNRZ1hUdUxzVnQrUVFsUU5nVXBGVUpmRXJUMVNaeURIclVhZEFoano2Q1BNWWRvRUM2TkJIcjRhZGl1R05ZeldMaXJld0JKaU4rTVZOSEluOTl6TnVQQ0JyQnMrN2dpU0l3ZkRrclh0dGFXak01RWI4WHFjR0EweDNLaVZhcStCS3JPY3B2b041ZEdObkNHYjY1UzNZMVpibngiLCJtYWMiOiIyNWU1MGQ3ZDgxMDM5YzE5YTJjNmY2ZjU4NjA0Yjk1NWY1ZGM4Mjc3NGU1ODhiYjNkYmI5YWI2MmFiYTM5NDlmIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjVDQkMxWlgrenFMd3RSbmxPcEtZK0E9PSIsInZhbHVlIjoidWVRZ2tQL1dTbGtNYlZ2RENaaklvSWNBQm5teENFblc4enh6cHREeHVrUTNWazRxdmVxRXFMdDVsRzVwR3VlK0krcnRPYlY2eHhWMjl1RG9YNDZhL0V6NzV6MHlVbjRYcUxidlRDNFdjcDEyaWdBS0xBVEtVODZaYnNHemRHY2hQMDJzcXg2TnpuZWtDekVRWWdMakM3NHRDTDFNUHphR3ZpMTNCeHdleUFaZm9DZGJZQStVZkgrRTc1WWZtWHM5WFhKcy9ySXVhdWJUK01GOE53SU9ZVTJBWTRLOENsd2ZtSy90QlloWUZhSzk4bGRVaFQ5ZWdjQVJ6MU14Mm1qWHRnVU9ONmd1RVk1alR4dFZ2OWtJLzdTYXhvUW1TakZTUGdSd2ltVUJNRG9PZ2xPbWYvMHNmcWNCV1hsSTg2RmR0cHhmaHFaVnBDTktkT2FIL2IrMEVIUnRFVzFaRWx0NWx1NmIxbFN0QmQ3WHZucjEwMEdvVncvVk5Fd2xpd3F4VVU2TWZ0cUlSdU1WRzY3aWtqL2hndDNaNERBV2JvSUliNFUvVGRTbGZnMTNOU3c0Zng3M0FxZDVLVEJwVE1McjdNMTdNTGFycjZ2QUx4eWFqMEFYSUhrSzg4TkN2WWdMei9seTNINFJZdlVmTXNLOXJMMkhVdUJXeExYWHl5TWUiLCJtYWMiOiI4ZDE0NzZiMzI2MWM0YTQwOGUxMTIwZmE3MDFkZWM1MmVkYzg4MmM4ODA2ODlhYzFhMzdjOWVhNzE3M2U4OWFjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9HNDY5cU9JYWxxeTZLR1UvUXJkb2c9PSIsInZhbHVlIjoiRm5FNWdEVDJwWlQ1eTRZb3ZOL3BZVTFRRmZUUlZCWFk0Q0ZWcFgxSGsxUXVOYlRmN011clhIdGFWOXI1d2I2c0ZGQnp2YURyOVZaeEZOdWJmZnBUMnBSR2w1YjJTMFZyNm9ZTklSVzVRSFYwUUoyTmRsN3d4bjNXUW1GZXhCc3dWcHprcGdJWWlxWmFhb0FRUG15Y3ZiUXFaZDdqSTlNVlg1OWhOVjVuTTBTU1NtM2hKVVBXYW9hUy9Fb3lXWFNFdGo4QVVtMUVibll3cWlUZjlEdTVHVzQ2Mmp2Tm0vY0cyNzkwWmNpaitxRmVBUFI1YWl2VHJoWFloaFJKSnZiT2tNTWQ4TnRWTDZ0WHNwd2dCLzNOUTlxUnJwNTkwRlNMeUIvalV5K0JmSzh0cVRYSkZGcWdTb3orOUwrUWJTRnBWSm9ISUoxcDBRZGQrQkxKbS9WRlVMY2JjaDN2R3pzYVF3TjV0VHNRZ1hUdUxzVnQrUVFsUU5nVXBGVUpmRXJUMVNaeURIclVhZEFoano2Q1BNWWRvRUM2TkJIcjRhZGl1R05ZeldMaXJld0JKaU4rTVZOSEluOTl6TnVQQ0JyQnMrN2dpU0l3ZkRrclh0dGFXak01RWI4WHFjR0EweDNLaVZhcStCS3JPY3B2b041ZEdObkNHYjY1UzNZMVpibngiLCJtYWMiOiIyNWU1MGQ3ZDgxMDM5YzE5YTJjNmY2ZjU4NjA0Yjk1NWY1ZGM4Mjc3NGU1ODhiYjNkYmI5YWI2MmFiYTM5NDlmIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjVDQkMxWlgrenFMd3RSbmxPcEtZK0E9PSIsInZhbHVlIjoidWVRZ2tQL1dTbGtNYlZ2RENaaklvSWNBQm5teENFblc4enh6cHREeHVrUTNWazRxdmVxRXFMdDVsRzVwR3VlK0krcnRPYlY2eHhWMjl1RG9YNDZhL0V6NzV6MHlVbjRYcUxidlRDNFdjcDEyaWdBS0xBVEtVODZaYnNHemRHY2hQMDJzcXg2TnpuZWtDekVRWWdMakM3NHRDTDFNUHphR3ZpMTNCeHdleUFaZm9DZGJZQStVZkgrRTc1WWZtWHM5WFhKcy9ySXVhdWJUK01GOE53SU9ZVTJBWTRLOENsd2ZtSy90QlloWUZhSzk4bGRVaFQ5ZWdjQVJ6MU14Mm1qWHRnVU9ONmd1RVk1alR4dFZ2OWtJLzdTYXhvUW1TakZTUGdSd2ltVUJNRG9PZ2xPbWYvMHNmcWNCV1hsSTg2RmR0cHhmaHFaVnBDTktkT2FIL2IrMEVIUnRFVzFaRWx0NWx1NmIxbFN0QmQ3WHZucjEwMEdvVncvVk5Fd2xpd3F4VVU2TWZ0cUlSdU1WRzY3aWtqL2hndDNaNERBV2JvSUliNFUvVGRTbGZnMTNOU3c0Zng3M0FxZDVLVEJwVE1McjdNMTdNTGFycjZ2QUx4eWFqMEFYSUhrSzg4TkN2WWdMei9seTNINFJZdlVmTXNLOXJMMkhVdUJXeExYWHl5TWUiLCJtYWMiOiI4ZDE0NzZiMzI2MWM0YTQwOGUxMTIwZmE3MDFkZWM1MmVkYzg4MmM4ODA2ODlhYzFhMzdjOWVhNzE3M2U4OWFjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603571376\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-411993418 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411993418\", {\"maxDepth\":0})</script>\n"}}