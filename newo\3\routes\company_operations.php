<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CompanyOperationsController;
use App\Http\Controllers\BranchCashManagementController;
use App\Http\Controllers\InventoryManagementController;
use App\Http\Controllers\BranchInventoryManagementController;
use App\Http\Controllers\VendorRepresentativeController;

Route::middleware(['auth', 'XSS'])->group(function () {
    // Camera Monitoring System
    Route::get('camera-monitoring', [CompanyOperationsController::class, 'cameraMonitoring'])->name('camera.monitoring');

    // Employee Tracking System
    Route::get('employee-tracking', [CompanyOperationsController::class, 'employeeTracking'])->name('employee.tracking');

    // WhatsApp Communication Management System
    Route::get('whatsapp-management', [CompanyOperationsController::class, 'whatsappManagement'])->name('whatsapp.management');

    // Branch Cash Management System
    Route::get('branch-cash-management', [BranchCashManagementController::class, 'index'])->name('branch.cash.management');
    Route::get('branch-cash-management/{id}', [BranchCashManagementController::class, 'show'])->name('branch.cash.management.show');
    Route::post('branch-cash-management/update/{id}', [BranchCashManagementController::class, 'updateFinancialRecord'])->name('branch.cash.management.update');
    Route::post('branch-cash-management/close-shift/{id}', [BranchCashManagementController::class, 'closeShift'])->name('branch.cash.management.close.shift');
    Route::post('branch-cash-management/reopen-shift/{id}', [BranchCashManagementController::class, 'reopenShift'])->name('branch.cash.management.reopen.shift');

    // Inventory Management System
    Route::get('inventory-management', [InventoryManagementController::class, 'index'])->name('inventory.management');
    Route::get('inventory-management/products/{warehouseId}', [InventoryManagementController::class, 'getWarehouseProducts'])->name('inventory.management.products');
    Route::post('inventory-management/update-quantity', [InventoryManagementController::class, 'updateQuantity'])->name('inventory.management.update.quantity');
    Route::post('inventory-management/update-min-quantity', [InventoryManagementController::class, 'updateMinQuantity'])->name('inventory.management.update.min.quantity');
    Route::post('inventory-management/update-product-data', [InventoryManagementController::class, 'updateProductData'])->name('inventory.management.update.product.data');
    Route::post('inventory-management/add-quantity-all', [InventoryManagementController::class, 'addQuantityToAll'])->name('inventory.add.quantity.all');
    Route::get('inventory-management/get-categories', [InventoryManagementController::class, 'getCategories'])->name('inventory.management.get.categories');
    Route::get('inventory-management/get-units', [InventoryManagementController::class, 'getUnits'])->name('inventory.management.get.units');
    Route::get('inventory-management/get-taxes', [InventoryManagementController::class, 'getTaxes'])->name('inventory.management.get.taxes');
    Route::get('inventory-management/print-report/{warehouseId}', [InventoryManagementController::class, 'printInventoryReport'])->name('inventory.management.print.report');

    // Branch Inventory Management System
    Route::get('branch-inventory-management', [BranchInventoryManagementController::class, 'index'])->name('branch.inventory.management');
    Route::get('branch-inventory-management/products/{warehouseId}', [BranchInventoryManagementController::class, 'getWarehouseProducts'])->name('branch.inventory.management.products');
    Route::post('branch-inventory-management/update-quantity', [BranchInventoryManagementController::class, 'updateQuantity'])->name('branch.inventory.management.update.quantity');
    Route::post('branch-inventory-management/update-min-quantity', [BranchInventoryManagementController::class, 'updateMinQuantity'])->name('branch.inventory.management.update.min.quantity');
    Route::post('branch-inventory-management/update-product-data', [BranchInventoryManagementController::class, 'updateProductData'])->name('branch.inventory.management.update.product.data');
    Route::get('branch-inventory-management/get-categories', [BranchInventoryManagementController::class, 'getCategories'])->name('branch.inventory.management.get.categories');
    Route::get('branch-inventory-management/get-units', [BranchInventoryManagementController::class, 'getUnits'])->name('branch.inventory.management.get.units');
    Route::get('branch-inventory-management/get-taxes', [BranchInventoryManagementController::class, 'getTaxes'])->name('branch.inventory.management.get.taxes');
    Route::get('branch-inventory-management/print-report/{warehouseId}', [BranchInventoryManagementController::class, 'printInventoryReport'])->name('branch.inventory.management.print.report');

    // Vendor Representatives Management System
    Route::get('vendor-representatives', [VendorRepresentativeController::class, 'index'])->name('vendor.representatives.index');
    Route::get('vendor-representatives/create', [VendorRepresentativeController::class, 'create'])->name('vendor.representatives.create');
    Route::post('vendor-representatives', [VendorRepresentativeController::class, 'store'])->name('vendor.representatives.store');
    Route::get('vendor-representatives/{id}', [VendorRepresentativeController::class, 'show'])->name('vendor.representatives.show');
    Route::get('vendor-representatives/{id}/edit', [VendorRepresentativeController::class, 'edit'])->name('vendor.representatives.edit');
    Route::put('vendor-representatives/{id}', [VendorRepresentativeController::class, 'update'])->name('vendor.representatives.update');
    Route::delete('vendor-representatives/{id}', [VendorRepresentativeController::class, 'destroy'])->name('vendor.representatives.destroy');
    Route::post('vendor-representatives/{id}/toggle-status', [VendorRepresentativeController::class, 'toggleStatus'])->name('vendor.representatives.toggle.status');
});
