{"__meta": {"id": "Xae4be5e072bb8bb371381f306d4f9fd2", "datetime": "2025-06-16 09:55:41", "utime": **********.984295, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067739.839352, "end": **********.984332, "duration": 2.1449801921844482, "duration_str": "2.14s", "measures": [{"label": "Booting", "start": 1750067739.839352, "relative_start": 0, "end": **********.735101, "relative_end": **********.735101, "duration": 1.8957490921020508, "duration_str": "1.9s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.735133, "relative_start": 1.8957810401916504, "end": **********.984337, "relative_end": 5.0067901611328125e-06, "duration": 0.24920415878295898, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45393744, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02547, "accumulated_duration_str": "25.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.879777, "duration": 0.02377, "duration_str": "23.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.325}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.946012, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.325, "width_percent": 6.675}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1706588681 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1706588681\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-103391568 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-103391568\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-176664544 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176664544\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNDckNhYWNIRXZJaXNLM29UU0ZjK3c9PSIsInZhbHVlIjoicDNUQTkwSzZjTXZ1c2hjd3E5YUY0ZjlaYld1WXNjbGtTNUE1ay9FbEw0UDhSOGFMOC9RMmNPRnF2SXlrUHlyYTk4U29WSEl2VXRUYjdZTXQ0Q0xhOUZmRCtYTUhwZjdDdm96ZWpIOG5lbFpBS2Ntc3RoZEV4K2dBWGdWcFR6QW5lMXVyZDVEbS9QbUIyZEdWUThnUXZRRGlSZUY2by8wWWRrT29oZko1UDNwcDRiMVU0N0Y1a3BTWW4zVkZPQmhtYmVvVmZzSncwamxVSWdTdStCUVpuT1pGMC90UlhYY2dwWTlySEJhNEZITDVTK2d0Mk95STBhUlhacWo4N0prNytHVkNweVpZZFpzdnByamNUQTFzQ1BNemJ4aldpMXpEeVdTME9UekRvRzhBamJXYUJaL0ZHSjBiQmppekxLajhvT254MkU3ZEpIRHJ3YjFPZG5KVkt0UWo2a21BNnpia3o5cXNVOW5EYmI1N3JFV3ZWbXprQlk5VUtQdWx4YWNMelpiejMya3FzZHI3bnMzQ3dVNCtiaW5mRTVWcWRYSlVORHAwUkxiQzlGMEdUZE92Nkc0NlMwd3lGTlNFN0kyNmhhcHNwR1BIZnFadWEvNCtFU0g5ZHhvS1pRZkxBMURENEpqSWppbnpCOEM4U2tta2tnQlBNRnZWdEIvcFFvZGwiLCJtYWMiOiIwYzdiZDU3NDYzYTU2NGRkZDQ0ZDE4ZTg2ZTNiYWVjZTNkODI3NWJlOGRlNWM5M2YwZGQxMjQwYjczNmM3ZDdiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InViazc5VXFsaHNDbHNkTjIvenB1SXc9PSIsInZhbHVlIjoibFdmZFRVVGI2dHpUMHFLdHRRcE5CdFdGMlQycnRkUVdOdFJkVkZKK3Z2am8vSDNkaHE5VjlqT2l3aDJaZldld0txS01jSU5DZ0d4azFtUDFocHUyaFROakM5NzJxWkJQeVN4OE5YV055UDhqRllnKzRybytqSTZ3ZXRzZGw4SG5iNHpNMnBITWpPTGxaOWlkdk9PYnN6MU9MamRRQ0w0L25Xd1g5NjNQV1p1ZmN3R1NHSGpZT0J3TnJGUTBvcVBDNUp2dWNFeDFhSjhnS2pGT2Z2eVg0RDdtaUdsbHowaXhQK2ViT2xBSkRnSkxxTTEzbzFETEVTaklDMHdBdzJIMnQzaDZ2OW5qMjZJWEtGaEsva1Z4L2hvZVRrRjRkbTFyYmpWR2lLOVg0TVpRaERaN2E0dEZ1UXBhSWk3WDdFN1Qvd2pmcmNhRTRYVXI2eXBhREtwUUZIcTREcWRpTjBHYzhaamJ5akdMazhOSkNsRmRUbGtVbGllRFMyZytIR2szVHBZUEJPQ3NjRkJ4NVE0a2NUbUMzMTBKTTM3T1kwdkhtYW8rcjNPbEJZVzA5dEhxWHhpNzJXc0Z0OG9oRG5UdHJYOEtZdG43LzNRVGFtQ3J5Qis1aW4yMDdrL29LTEtUYzZ6eTB2SFBMbGd5eWFEQWI4NWFlM3RKQmRselFCY04iLCJtYWMiOiI5NmZhZmFjNGVhZjliMmRmYTg2YjRmMzFlNzkwM2I2NjJiZTM2NjcyMzE5YzJmOTE3NGE2NjQ2ZTRhZWJiMDZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-921952108 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921952108\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1570438489 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:55:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFDSlNNS1VtdTl4d29kdU1EbEw3U1E9PSIsInZhbHVlIjoiaTRsdmZzaUg1YlZEMDdIWXU5b1hJM0dFVGZxVnVXWmxhUmc2QWZVYS9lUzhOYmhWNmxRTTFDdUk2eDNBUDJoa1NlVXhsTVQ0Z1JFZGxUMVExbFJJZU53S3ZqWGMzRTI5RE0xem05YXpxNGJIKzFXZTdxbzgyb1diV2FzWDVrdmhzenBtcVRMN0xydjBqVmdRekMvYTc0bU0rQzE5UUt6NDQyWW9qb0g5a0ZIOVdteEdSVE9CSm1hVXBzQy84cDVSY2xya1pWRzZRUWhRMXRVT0gxNnRPQmdUZXBMb05MMTZVWFdLdXBaNWZBbHlOZjdBQ05UVm5lTUVSWlQ1Qk1yU1UzNnZjN0o1THQ1d0ttWm9PdWFtK0FhNkpYQW9BVE0yOWl1YjN2ejVERVJjaC9HeWRaaXRYVVpQVXJrZDByZjBjY1BuRjBvMFpsZFFsT0cwOWYxeU5hWlQrK2U5M0RuTkNlcGZ4UDJrSllEWHZwOHd4UHdpVllMR28zMEJqRjhubkUrVTY0L3RCbXFDWitrSWlBRDc0VkNDUFZEVnVoMml5MGM0bzFXU3A3T2hPaUxSd0NWdEpjbVZyT1IxNUdTcmpRaWhxcS9PMU5rY0c2VFJPd2RIZDQ2TzB1UTFEdSsrbzNTQUt5VFh4aGx1c0VIOURHcW5ESE4wZTBFWG5xYW4iLCJtYWMiOiIzMDkzYjg5OTVhNDRlNjM2NTI5NWJlNjZiYTUwMDI4YjY2ODA2MTY4MDNkNmVhYWNjM2NmYzkyOTM2NTNjYWJjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikl0c1UvSHh6NW1WUU02bWNvanNZb0E9PSIsInZhbHVlIjoiT0Q2ZGNGQVowWXZEZjk1VVdhRk5Cay94aWFQN2tVWjFtbFBxeG4rMnVQN0JIc2k3c1Z2Sk9zWElBaGNoN0x4RDZDRFRHV2tRUGZjS3pVdlpMMnBGMUg3Y3JWZ2Fwb0dOYXIwL2VHdG51VmRhbzdaaVgwMEFXRkxUTXIvekNENkJCb01Id1VCamhyTUQzMTBkT00yQ3RQeXd4M3dmN0dFeXVJbnkyVXNBWlY0NXEweHFiSXp4SWlKaVdySkZJcURvWlFTMmpTTUV6VUFybXFDUEpTZnE3dFlnRE9BalNpOU5Da1NmVFFLR1pETnJMbDVZTFJValA5R3VqaDVFTDJpeDYzbE5MbnBWNkJwcTArenNjK3VMZWlqL1RLMFk4QnkvamNyTHdDMnZNV2JxejZPV1ZYV2lsa293UEVmVjU3OE9lYlRzZVdlZUQrcjM1M2tNNHcrbnJnK0ZVM0ZmeEVsaTN0Unh5T0Zra29BYjZFS2tWT1FCU3Ziby8wWmUzMlJoZHZzZjRsdU9rMms2UVZneXNHOENFUXhwRzBNeGEzVGJFZ2gxemhHK1VMSVFVT1BBUG9Ec0lvZnNKZmRWTWV1ZmZxVThDMDJuOWoxU0FOWldLY2ZSTWpQWUcxdUNodkNIUDNKdkhHVU9SWjZrc1M5R0ozeWtBeEY2R3AyVXFtWTQiLCJtYWMiOiIxNTU3MzljNzExMTIwMWIwMDI1ZjMyYzE2ZjI1NzU1ZjlhNzcwZmEwMzM1YmU5YzJjZjcxMmQ2YWY5Yjk4MTVjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFDSlNNS1VtdTl4d29kdU1EbEw3U1E9PSIsInZhbHVlIjoiaTRsdmZzaUg1YlZEMDdIWXU5b1hJM0dFVGZxVnVXWmxhUmc2QWZVYS9lUzhOYmhWNmxRTTFDdUk2eDNBUDJoa1NlVXhsTVQ0Z1JFZGxUMVExbFJJZU53S3ZqWGMzRTI5RE0xem05YXpxNGJIKzFXZTdxbzgyb1diV2FzWDVrdmhzenBtcVRMN0xydjBqVmdRekMvYTc0bU0rQzE5UUt6NDQyWW9qb0g5a0ZIOVdteEdSVE9CSm1hVXBzQy84cDVSY2xya1pWRzZRUWhRMXRVT0gxNnRPQmdUZXBMb05MMTZVWFdLdXBaNWZBbHlOZjdBQ05UVm5lTUVSWlQ1Qk1yU1UzNnZjN0o1THQ1d0ttWm9PdWFtK0FhNkpYQW9BVE0yOWl1YjN2ejVERVJjaC9HeWRaaXRYVVpQVXJrZDByZjBjY1BuRjBvMFpsZFFsT0cwOWYxeU5hWlQrK2U5M0RuTkNlcGZ4UDJrSllEWHZwOHd4UHdpVllMR28zMEJqRjhubkUrVTY0L3RCbXFDWitrSWlBRDc0VkNDUFZEVnVoMml5MGM0bzFXU3A3T2hPaUxSd0NWdEpjbVZyT1IxNUdTcmpRaWhxcS9PMU5rY0c2VFJPd2RIZDQ2TzB1UTFEdSsrbzNTQUt5VFh4aGx1c0VIOURHcW5ESE4wZTBFWG5xYW4iLCJtYWMiOiIzMDkzYjg5OTVhNDRlNjM2NTI5NWJlNjZiYTUwMDI4YjY2ODA2MTY4MDNkNmVhYWNjM2NmYzkyOTM2NTNjYWJjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikl0c1UvSHh6NW1WUU02bWNvanNZb0E9PSIsInZhbHVlIjoiT0Q2ZGNGQVowWXZEZjk1VVdhRk5Cay94aWFQN2tVWjFtbFBxeG4rMnVQN0JIc2k3c1Z2Sk9zWElBaGNoN0x4RDZDRFRHV2tRUGZjS3pVdlpMMnBGMUg3Y3JWZ2Fwb0dOYXIwL2VHdG51VmRhbzdaaVgwMEFXRkxUTXIvekNENkJCb01Id1VCamhyTUQzMTBkT00yQ3RQeXd4M3dmN0dFeXVJbnkyVXNBWlY0NXEweHFiSXp4SWlKaVdySkZJcURvWlFTMmpTTUV6VUFybXFDUEpTZnE3dFlnRE9BalNpOU5Da1NmVFFLR1pETnJMbDVZTFJValA5R3VqaDVFTDJpeDYzbE5MbnBWNkJwcTArenNjK3VMZWlqL1RLMFk4QnkvamNyTHdDMnZNV2JxejZPV1ZYV2lsa293UEVmVjU3OE9lYlRzZVdlZUQrcjM1M2tNNHcrbnJnK0ZVM0ZmeEVsaTN0Unh5T0Zra29BYjZFS2tWT1FCU3Ziby8wWmUzMlJoZHZzZjRsdU9rMms2UVZneXNHOENFUXhwRzBNeGEzVGJFZ2gxemhHK1VMSVFVT1BBUG9Ec0lvZnNKZmRWTWV1ZmZxVThDMDJuOWoxU0FOWldLY2ZSTWpQWUcxdUNodkNIUDNKdkhHVU9SWjZrc1M5R0ozeWtBeEY2R3AyVXFtWTQiLCJtYWMiOiIxNTU3MzljNzExMTIwMWIwMDI1ZjMyYzE2ZjI1NzU1ZjlhNzcwZmEwMzM1YmU5YzJjZjcxMmQ2YWY5Yjk4MTVjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570438489\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-745083068 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745083068\", {\"maxDepth\":0})</script>\n"}}