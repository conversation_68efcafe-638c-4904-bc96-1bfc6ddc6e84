@extends('layouts.admin')

@section('page-title')
    {{ __('إعدادات المخزون السالب') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('إعدادات المخزون السالب') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('إعدادات البيع على الكميات السالبة') }}</h5>
                    <small class="text-muted">{{ __('تحكم في إمكانية البيع عندما تكون الكمية صفر أو سالبة') }}</small>
                </div>
                <div class="card-body">
                    {{ Form::open(['route' => 'negative.stock.settings.store', 'method' => 'POST']) }}
                    
                    <div class="row">
                        <div class="col-lg-6 col-md-6 col-sm-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>{{ __('السماح بالبيع على الكميات السالبة') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="form-check-label" for="allow_negative_stock">
                                            {{ __('تفعيل البيع على الكميات الصفرية والسالبة') }}
                                        </label>
                                        <div class="form-check form-switch">
                                            <input type="checkbox" class="form-check-input" 
                                                   name="allow_negative_stock" id="allow_negative_stock" 
                                                   {{ Utility::getValByName('allow_negative_stock') == 'on' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="allow_negative_stock"></label>
                                        </div>
                                        <small class="text-muted">
                                            {{ __('عند التفعيل: يمكن بيع المنتجات حتى لو كانت الكمية صفر أو سالبة') }}<br>
                                            {{ __('عند التعطيل: لا يمكن بيع المنتجات إذا كانت الكمية صفر أو أقل') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 col-md-6 col-sm-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>{{ __('معلومات إضافية') }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <h6>{{ __('ملاحظات مهمة:') }}</h6>
                                        <ul class="mb-0">
                                            <li>{{ __('عند تفعيل هذا الإعداد، ستظهر جميع المنتجات في POS حتى لو كانت كمياتها صفر') }}</li>
                                            <li>{{ __('سيتم تسجيل الكميات بالسالب في المستودع عند البيع') }}</li>
                                            <li>{{ __('يمكن استخدام هذا الإعداد للبيع المؤجل أو البيع بالطلب') }}</li>
                                            <li>{{ __('تأكد من مراقبة المخزون السالب بانتظام') }}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="text-end">
                                <input type="submit" value="{{ __('حفظ الإعدادات') }}" class="btn btn-primary">
                            </div>
                        </div>
                    </div>
                    
                    {{ Form::close() }}
                </div>
            </div>
        </div>
    </div>
@endsection
