{"__meta": {"id": "X9440844c1768d2a6cce3e0dd73fcc39b", "datetime": "2025-06-16 09:55:45", "utime": **********.203802, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067742.439288, "end": **********.203891, "duration": 2.7646031379699707, "duration_str": "2.76s", "measures": [{"label": "Booting", "start": 1750067742.439288, "relative_start": 0, "end": 1750067744.898074, "relative_end": 1750067744.898074, "duration": 2.4587860107421875, "duration_str": "2.46s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750067744.89811, "relative_start": 2.458822011947632, "end": **********.203899, "relative_end": 7.867813110351562e-06, "duration": 0.3057889938354492, "duration_str": "306ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45276528, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01015, "accumulated_duration_str": "10.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0897431, "duration": 0.00698, "duration_str": "6.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.768}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1475542, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.768, "width_percent": 15.172}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.165967, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 83.941, "width_percent": 16.059}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1842294260 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1842294260\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-15387595 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15387595\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-568357721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-568357721\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-775790103 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFDSlNNS1VtdTl4d29kdU1EbEw3U1E9PSIsInZhbHVlIjoiaTRsdmZzaUg1YlZEMDdIWXU5b1hJM0dFVGZxVnVXWmxhUmc2QWZVYS9lUzhOYmhWNmxRTTFDdUk2eDNBUDJoa1NlVXhsTVQ0Z1JFZGxUMVExbFJJZU53S3ZqWGMzRTI5RE0xem05YXpxNGJIKzFXZTdxbzgyb1diV2FzWDVrdmhzenBtcVRMN0xydjBqVmdRekMvYTc0bU0rQzE5UUt6NDQyWW9qb0g5a0ZIOVdteEdSVE9CSm1hVXBzQy84cDVSY2xya1pWRzZRUWhRMXRVT0gxNnRPQmdUZXBMb05MMTZVWFdLdXBaNWZBbHlOZjdBQ05UVm5lTUVSWlQ1Qk1yU1UzNnZjN0o1THQ1d0ttWm9PdWFtK0FhNkpYQW9BVE0yOWl1YjN2ejVERVJjaC9HeWRaaXRYVVpQVXJrZDByZjBjY1BuRjBvMFpsZFFsT0cwOWYxeU5hWlQrK2U5M0RuTkNlcGZ4UDJrSllEWHZwOHd4UHdpVllMR28zMEJqRjhubkUrVTY0L3RCbXFDWitrSWlBRDc0VkNDUFZEVnVoMml5MGM0bzFXU3A3T2hPaUxSd0NWdEpjbVZyT1IxNUdTcmpRaWhxcS9PMU5rY0c2VFJPd2RIZDQ2TzB1UTFEdSsrbzNTQUt5VFh4aGx1c0VIOURHcW5ESE4wZTBFWG5xYW4iLCJtYWMiOiIzMDkzYjg5OTVhNDRlNjM2NTI5NWJlNjZiYTUwMDI4YjY2ODA2MTY4MDNkNmVhYWNjM2NmYzkyOTM2NTNjYWJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikl0c1UvSHh6NW1WUU02bWNvanNZb0E9PSIsInZhbHVlIjoiT0Q2ZGNGQVowWXZEZjk1VVdhRk5Cay94aWFQN2tVWjFtbFBxeG4rMnVQN0JIc2k3c1Z2Sk9zWElBaGNoN0x4RDZDRFRHV2tRUGZjS3pVdlpMMnBGMUg3Y3JWZ2Fwb0dOYXIwL2VHdG51VmRhbzdaaVgwMEFXRkxUTXIvekNENkJCb01Id1VCamhyTUQzMTBkT00yQ3RQeXd4M3dmN0dFeXVJbnkyVXNBWlY0NXEweHFiSXp4SWlKaVdySkZJcURvWlFTMmpTTUV6VUFybXFDUEpTZnE3dFlnRE9BalNpOU5Da1NmVFFLR1pETnJMbDVZTFJValA5R3VqaDVFTDJpeDYzbE5MbnBWNkJwcTArenNjK3VMZWlqL1RLMFk4QnkvamNyTHdDMnZNV2JxejZPV1ZYV2lsa293UEVmVjU3OE9lYlRzZVdlZUQrcjM1M2tNNHcrbnJnK0ZVM0ZmeEVsaTN0Unh5T0Zra29BYjZFS2tWT1FCU3Ziby8wWmUzMlJoZHZzZjRsdU9rMms2UVZneXNHOENFUXhwRzBNeGEzVGJFZ2gxemhHK1VMSVFVT1BBUG9Ec0lvZnNKZmRWTWV1ZmZxVThDMDJuOWoxU0FOWldLY2ZSTWpQWUcxdUNodkNIUDNKdkhHVU9SWjZrc1M5R0ozeWtBeEY2R3AyVXFtWTQiLCJtYWMiOiIxNTU3MzljNzExMTIwMWIwMDI1ZjMyYzE2ZjI1NzU1ZjlhNzcwZmEwMzM1YmU5YzJjZjcxMmQ2YWY5Yjk4MTVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775790103\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1520317552 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1520317552\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:55:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdmZ0czU1cyR283NW9XS2lxZ2lmRHc9PSIsInZhbHVlIjoiOHlsbHY5Nmc2UjRmKzljZm9TMHo2d0NaTVJoTEFyM3FDbnFvcEV6V3ZKQktMUnFYVEhSRDlyOForRGVpUUl2UXZ1eXF0TEFpNEZLR1dRb3g1UEJtUll4RDhFMzA4T2RqRTV5MzZ5Sjd0ZU1hYjhCZHQxK2o2OGF3YUl6c0dia3VIWk1kazJuT054VUZEZml2OGhpYlFLUFphTG1ldk83Vi9YN0FyK083TGFrWE5rU0t3dmQ1dU9nSHpDRFAxSnBlTjBJaUJncDhQMzBLVlllWkxtSkI0MGlCVk56UXJoUEU1VVp5UE13QkxudEVVSk9QRW16cmJTamRXTUlVZ0tSRmJFZTU4UlpQeU9WWWpaNjFURitBaGZqcTMxVnpTNzNHV0R6WXduUzZFZW5kN3U2Vkc4TndTRDN2TEs1UWcySSt3eUhOaVVGM3V2RExIWXR1cUlNYzR1cElHeExUblU5UkptSEdROEFUMXUwejhIRFZkR1R3UjhoL0xkdkxWSlR6dTVqaHRLTS9jUm9ZWk43NU1qYU8zOTJSVkNDOHlBcWNjWllIdnN2NnhleWxKa2hmamF6QzB0S3ZEN0ZNL2hJdS9vZytYczgxUmJ2ckNjR3FRaUl5Nk5sYmd4RXVRL2V1YUtDM1FLRHdyK3Y3djE3YkFmaHNMaHY0ODNzZU5sK2giLCJtYWMiOiJkMzc4YTkxY2RhOGQ2NTcyZjY2N2E5NmJhYWYxNWM1NDIyOGExYWM3YWNkYzNjYzJhMmFiYTk2YTQwYTk2ODAzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJ0N1gvWCtlaUd6SjVvTXd4TG1WaEE9PSIsInZhbHVlIjoiS1F6Y3l5SDh1YXBPelRwUitXRytmYXFvZGhYWnpzb0YrNXJCdWt3RTA3UVBFSGRpWUozMkNua252M2dxY0JVWk5pUWFkV0JOb0hBd0daM0xONk82S2htZlI5Z0NoYjh1ZGU1eDVYU2ZSeE8yL1lZQzcvV0xOWjBPR09HQ1QxQmtPZUNicUdnSnpLVmFnRU1raTVSdFE3Q3hCejVGVENnOUZ1d3p1VW9WVnExUjNMajZOZlRZUW1xVXd4LzV4RERiZE8rWTFnalA3T0F6Yzh4Q0dOMEhWb08rMGpRKzdFQWZ0QnJ4ZFdYVG5XTTNiRUs2OEJ4OU1DV3V1SlFZRHczSHQ3TTl5a2phZk9qdEJPY3lQZ2JFUkhpRnJIbEV2cCtnaStUa0x5eHg1K0w2TmxXK09iODlyYW1xWml5anc4ZjlXUk5nREk0UTdacW5ZdmNUVFRwblFIUHp3WHVrK0ZiV2NhcW5IbTV1TExwTmYvMGpuSC9PUUtIemlVR2VISWlYelF3eTdWZERZRVFLNGJvU0tLMTc3bzdpSTc4ZFRNa092OHhwZW5lVVJiKzFzVW5xT2xYQjZyT3J5U2h2T3FDN3ZncWwzd2FnM1dPUmhab0RhNVFISHVUUHBIbWxISmc2ZUpYNU1FQ1kwVUdHeHYyTXlzRHd5VkFBMmExWUxKTlMiLCJtYWMiOiI4ZDRlMGNmMTY5YzE0NjdlMWQwOWU0NDViMmYwOTg0Nzk1M2FmNGRjNzQ4MGVlYTM0YzA4YTk3YmE1ZGZiMDFhIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:55:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdmZ0czU1cyR283NW9XS2lxZ2lmRHc9PSIsInZhbHVlIjoiOHlsbHY5Nmc2UjRmKzljZm9TMHo2d0NaTVJoTEFyM3FDbnFvcEV6V3ZKQktMUnFYVEhSRDlyOForRGVpUUl2UXZ1eXF0TEFpNEZLR1dRb3g1UEJtUll4RDhFMzA4T2RqRTV5MzZ5Sjd0ZU1hYjhCZHQxK2o2OGF3YUl6c0dia3VIWk1kazJuT054VUZEZml2OGhpYlFLUFphTG1ldk83Vi9YN0FyK083TGFrWE5rU0t3dmQ1dU9nSHpDRFAxSnBlTjBJaUJncDhQMzBLVlllWkxtSkI0MGlCVk56UXJoUEU1VVp5UE13QkxudEVVSk9QRW16cmJTamRXTUlVZ0tSRmJFZTU4UlpQeU9WWWpaNjFURitBaGZqcTMxVnpTNzNHV0R6WXduUzZFZW5kN3U2Vkc4TndTRDN2TEs1UWcySSt3eUhOaVVGM3V2RExIWXR1cUlNYzR1cElHeExUblU5UkptSEdROEFUMXUwejhIRFZkR1R3UjhoL0xkdkxWSlR6dTVqaHRLTS9jUm9ZWk43NU1qYU8zOTJSVkNDOHlBcWNjWllIdnN2NnhleWxKa2hmamF6QzB0S3ZEN0ZNL2hJdS9vZytYczgxUmJ2ckNjR3FRaUl5Nk5sYmd4RXVRL2V1YUtDM1FLRHdyK3Y3djE3YkFmaHNMaHY0ODNzZU5sK2giLCJtYWMiOiJkMzc4YTkxY2RhOGQ2NTcyZjY2N2E5NmJhYWYxNWM1NDIyOGExYWM3YWNkYzNjYzJhMmFiYTk2YTQwYTk2ODAzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJ0N1gvWCtlaUd6SjVvTXd4TG1WaEE9PSIsInZhbHVlIjoiS1F6Y3l5SDh1YXBPelRwUitXRytmYXFvZGhYWnpzb0YrNXJCdWt3RTA3UVBFSGRpWUozMkNua252M2dxY0JVWk5pUWFkV0JOb0hBd0daM0xONk82S2htZlI5Z0NoYjh1ZGU1eDVYU2ZSeE8yL1lZQzcvV0xOWjBPR09HQ1QxQmtPZUNicUdnSnpLVmFnRU1raTVSdFE3Q3hCejVGVENnOUZ1d3p1VW9WVnExUjNMajZOZlRZUW1xVXd4LzV4RERiZE8rWTFnalA3T0F6Yzh4Q0dOMEhWb08rMGpRKzdFQWZ0QnJ4ZFdYVG5XTTNiRUs2OEJ4OU1DV3V1SlFZRHczSHQ3TTl5a2phZk9qdEJPY3lQZ2JFUkhpRnJIbEV2cCtnaStUa0x5eHg1K0w2TmxXK09iODlyYW1xWml5anc4ZjlXUk5nREk0UTdacW5ZdmNUVFRwblFIUHp3WHVrK0ZiV2NhcW5IbTV1TExwTmYvMGpuSC9PUUtIemlVR2VISWlYelF3eTdWZERZRVFLNGJvU0tLMTc3bzdpSTc4ZFRNa092OHhwZW5lVVJiKzFzVW5xT2xYQjZyT3J5U2h2T3FDN3ZncWwzd2FnM1dPUmhab0RhNVFISHVUUHBIbWxISmc2ZUpYNU1FQ1kwVUdHeHYyTXlzRHd5VkFBMmExWUxKTlMiLCJtYWMiOiI4ZDRlMGNmMTY5YzE0NjdlMWQwOWU0NDViMmYwOTg0Nzk1M2FmNGRjNzQ4MGVlYTM0YzA4YTk3YmE1ZGZiMDFhIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:55:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1814177056 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814177056\", {\"maxDepth\":0})</script>\n"}}