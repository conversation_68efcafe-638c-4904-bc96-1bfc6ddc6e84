{"__meta": {"id": "Xc63b3083d04f45b1dd48c235ab26a5e5", "datetime": "2025-06-16 08:50:54", "utime": **********.869397, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063853.504968, "end": **********.869429, "duration": 1.3644611835479736, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1750063853.504968, "relative_start": 0, "end": **********.716282, "relative_end": **********.716282, "duration": 1.2113139629364014, "duration_str": "1.21s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.716311, "relative_start": 1.2113430500030518, "end": **********.869433, "relative_end": 3.814697265625e-06, "duration": 0.1531219482421875, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45164160, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01006, "accumulated_duration_str": "10.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7996302, "duration": 0.00645, "duration_str": "6.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.115}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.831044, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.115, "width_percent": 17.495}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8484652, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.61, "width_percent": 18.39}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063844760%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ill6dURUVFdOVnowdkJ1KzhrVzN0b3c9PSIsInZhbHVlIjoibzN5VGZNV1ZNbjl2QnJMQjdNaU81R1I2bDdVVnA2c2VpQ3lHditPQlJ5RDRQSjNtd1VWd2Fxd3RHeE9Kb0NIM0xIRTBiQndRdVpaZk5sZFdNcVNLTHliOHhhc1NRLzYzSzQ3V1AvbGlSYlNkakhZbmd0emM5VkxvNUpOaS9OSGV2YWp0cDV4enk2VTMycTJSTGFTZWRYZHlaTkRpaTB2eTZjQjlkeTVZSzQvWGF1RXVhM3M5VXRMUVRHaVY1LzRqV2xDVUZrNzNncDUxcUl1WVFEM1Z4Zjlxc05oa0NmYjBKM2NxRHF1YWY0SFgwdG5oRmNaVmtnb3FVZmNER3ppcXR1aHRFUmIzMnN0ODBDbFVzRUdiNzRpb0FIemNXYWZtOEpsNktmaCt0M2tzSWNIaVhtUXB6T1lHSWp2ajZJMXppaXlHRjlKalFoOHFUMEZtNGFzc0lnTG9mZEFJS2d5eHZEaHlHVmk3VEdWN2hqS2ZqYzFjeHIxRWZXMUNVMzFKbW9vWE1CUU9CcDdUMitobDdwSytOWHZyT3V5eFJZSjBBMDRvRFVlUmdUNUpxTGNRS0hWcnQzYnoyaVVjMFVaekNHQ3BIWWZuaHNsZEQ1S0NPTXY4aUR3QVNkOXFDVmxGTnEzMENreDdlREdIUlhTVEs5LzU0Qlg2MzZUUGVQdHoiLCJtYWMiOiJiYzNmYWYzMjg5MDFjMWZmZmYyZjhhMWM0MzFmNDI1N2Q5MmQyZTg2ZTU2ODJjYzI2OTRlY2EyYWNiM2NkMTQzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImF2bHJZRHJiMkYxME1QUU02eUdCenc9PSIsInZhbHVlIjoiRjNVTnk4cmxJWW43YldGRUw1bkVHYm9lNlFGYlkxTjBBd2xvSnhjTGczZ20vbzFXTW9WeFpjbzU0QzBpc2grQW9ZWk5mTTBndUxCWHNLL2lQRjZLUmVlMmgxQlM2U3ZIZEFzTXd5WXpjRHVtL0ZrNHZMVmtZemptRURTYVI5UUdmb1plWkdnS0tYVnVwMzc1TWxhMG14MjdZcjhpb0szRXI5U29qT2V6SGVnOURaM3JBR0F3UzlRd25wTWhsdUpBRUtFWlpBR0N6dm1zSGR6Y3hiSlNZVUxpUTl6QnU3K2VuSFN5U1dXMUx0UzJxMm9od3JMNE1MbXhvSmdjNForTHNvdWx4MnplL1RsSjg5bjJKTHpVbGhtY3FKUkphbHFSUW03aWtmZllEYWxGVUIwU1BnNGxSWkRNVnlEZmE5SkJ3bTZGK3ExVFpRR0dZUXRwdWJUeFd5ZlhKaGlId2VsZ2tZNUM5Z29SZ2VhTEFZZGxKOFNyN0FaNGhjbWFFaTZuSmx0SWJzT2dRSFVHS1J0dVhoaHdGUTUxcm45K0FSekZtNUlpdnNDeWp2dHR2QnlYVkJ5VnBuZmg4NVRhZUFBU1NEL2RCVlJqN0k5Wlo2bVlGcHJiYkZOT2lLaS9udGpYME9mQk5sa3ZsTTRGYkppM0JOVWtKZ0QvWkk1eTRSN3EiLCJtYWMiOiIwODU0NjUyMmYzYjA1OTJkOGI4ZDVhYWRiNGZlMzk1MjA2YjgyNzAzNzU3MGJjNDAzNmI5ODQ2ODMxNmM4MjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-322863732 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322863732\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRldjJBTlZvTVFkYlVPTXVDMzVHa1E9PSIsInZhbHVlIjoiRGlIMHlVTEJ3T21qMWdWV0ZORkJWUG8yRmJpbExOYWRCd3Jvd3pINGI4STBqTlg3UjBXMitzdEpDQy9nYkJuUEduRmhoTFdIUVdweCt2cGZRWTZwTU43azhmeXJTNkNFRXNXNGE4Sm03UW1CSE52RVB2N1dDajNCOE1lcW5Gb2daUnpkVGJYdTFzTTl5OHdSNWNkczF6TnZlY2xVNE1LaElzMVZXM2dZc2JOMHUzQ21ZVjhVZWRCdXJJZno1QXkvSmliajBVUjVCRWdwUzlieEVvQUFLSlRiTWNPWjg5V0graWM2amJCZTl1dkNET2RLQVJGQmZvcUEyVGNpNlF5d3dlTDZoVmlsTlVxY3VES1RMUUpCZzBveE1id01Vak45YUp3RTJHdjhLcUh0ZE9NNUg3WGM2SFdIVHdweWMxV2N4emUxWGdwK2lieEZnR0RqTVZsVnJ6amdBREpiTGY0NTdySlcwRi9jWjNvU1E3cmR4WVNKQzl4anhCM2ZmelRKNlhuRVR0c0N2TGFnZzBUVlhEQ3F1WlpzOEpaY2UxU2lzQldFcDZPb3NIaE8vMFFGSk9JQU15ZG4xL0lWcVBZcitMa3J6ZzBSN0JERFFQaEhVK0VuaUcvVzJZTldsaWJzQzlmUVUrQzF0WUJndGJIUkZBQmNyQXlxSlh2S0JjWEYiLCJtYWMiOiJhYWYwNWZmNGExZjRkMTM5MDlkOGQyY2UxZTgwYjhmY2UxMGI3OGFmNTA1MTM5ZjM3ZDNjOWVlMmNhNjE4NWI0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImJyc095TnBkN0F5NDZCdGpoaStZd0E9PSIsInZhbHVlIjoibmVOM2xmTjNCdmZiVFNKaE1xM0syMWZzWVVKZEdKYkdLWHJ0K0JCNHJ0MUVleWZObDNSaFlIbXJQY002WWloaEU1YWZIZHdKcU5yR2Vnc0FIRTZnSTVsSHREVHlLbGd6T1ErYWxHbkVHZ2xzRS95NHJvVlhSZ2p4UWZNWWZXeWcvajl0UFZXc1BKdEx5dVJWYStsY1M3YTBZMnJPS0tTWGZicUFBWEk1cGhQWlBidjNhYTNUOWY4VTgwZkhxOUQweG94bzBTdWRjQWNxVUZoN2xWZ1Awb05oc0tXUHc4cGtTZUhkbEpVY0NZN0RJQ29pcCtSVjdlQ01EZThwV3M4Ymt6SUtZQ0F3M2k5Ry92cGQvVkFiWWxvZUNwTmEzVzVoMXRoMVBiaDU0ZkRzb1NhWlM2SlNpWkZ6SzUvVkZlWENlZXlRWmRjWkIvd1VtRFh2aEVXWlVkOFdEMFh4clZzcGF4b3UrQ1J5QXpVb1F5N2luSC9qVUtxZTdNVnNJSWsraHpaSnhEWkNZZDRubGpHdU1OaFlwcUpCWXc3NzRVRkhuZ1p2MHVDVWlOV0hxTGlRVkd4ajhQZlNuZmFhWS81TE9NeWd6U1IrN0NrcWZyYzIzVElGb2Y3SlZoQ3A1NFJkRHhCV2lVSm9sb05lT3hIRVptd2syVjcvWEpFT1lTKzYiLCJtYWMiOiI3NDNlMDY4MGM5ODE1OTFiMzc2Nzg4ZDRjN2U0ZjJkZmFhZDI3NzU5ZTBiNTFjNzQyMmNhMTNkZWM4NmNlMTdlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRldjJBTlZvTVFkYlVPTXVDMzVHa1E9PSIsInZhbHVlIjoiRGlIMHlVTEJ3T21qMWdWV0ZORkJWUG8yRmJpbExOYWRCd3Jvd3pINGI4STBqTlg3UjBXMitzdEpDQy9nYkJuUEduRmhoTFdIUVdweCt2cGZRWTZwTU43azhmeXJTNkNFRXNXNGE4Sm03UW1CSE52RVB2N1dDajNCOE1lcW5Gb2daUnpkVGJYdTFzTTl5OHdSNWNkczF6TnZlY2xVNE1LaElzMVZXM2dZc2JOMHUzQ21ZVjhVZWRCdXJJZno1QXkvSmliajBVUjVCRWdwUzlieEVvQUFLSlRiTWNPWjg5V0graWM2amJCZTl1dkNET2RLQVJGQmZvcUEyVGNpNlF5d3dlTDZoVmlsTlVxY3VES1RMUUpCZzBveE1id01Vak45YUp3RTJHdjhLcUh0ZE9NNUg3WGM2SFdIVHdweWMxV2N4emUxWGdwK2lieEZnR0RqTVZsVnJ6amdBREpiTGY0NTdySlcwRi9jWjNvU1E3cmR4WVNKQzl4anhCM2ZmelRKNlhuRVR0c0N2TGFnZzBUVlhEQ3F1WlpzOEpaY2UxU2lzQldFcDZPb3NIaE8vMFFGSk9JQU15ZG4xL0lWcVBZcitMa3J6ZzBSN0JERFFQaEhVK0VuaUcvVzJZTldsaWJzQzlmUVUrQzF0WUJndGJIUkZBQmNyQXlxSlh2S0JjWEYiLCJtYWMiOiJhYWYwNWZmNGExZjRkMTM5MDlkOGQyY2UxZTgwYjhmY2UxMGI3OGFmNTA1MTM5ZjM3ZDNjOWVlMmNhNjE4NWI0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImJyc095TnBkN0F5NDZCdGpoaStZd0E9PSIsInZhbHVlIjoibmVOM2xmTjNCdmZiVFNKaE1xM0syMWZzWVVKZEdKYkdLWHJ0K0JCNHJ0MUVleWZObDNSaFlIbXJQY002WWloaEU1YWZIZHdKcU5yR2Vnc0FIRTZnSTVsSHREVHlLbGd6T1ErYWxHbkVHZ2xzRS95NHJvVlhSZ2p4UWZNWWZXeWcvajl0UFZXc1BKdEx5dVJWYStsY1M3YTBZMnJPS0tTWGZicUFBWEk1cGhQWlBidjNhYTNUOWY4VTgwZkhxOUQweG94bzBTdWRjQWNxVUZoN2xWZ1Awb05oc0tXUHc4cGtTZUhkbEpVY0NZN0RJQ29pcCtSVjdlQ01EZThwV3M4Ymt6SUtZQ0F3M2k5Ry92cGQvVkFiWWxvZUNwTmEzVzVoMXRoMVBiaDU0ZkRzb1NhWlM2SlNpWkZ6SzUvVkZlWENlZXlRWmRjWkIvd1VtRFh2aEVXWlVkOFdEMFh4clZzcGF4b3UrQ1J5QXpVb1F5N2luSC9qVUtxZTdNVnNJSWsraHpaSnhEWkNZZDRubGpHdU1OaFlwcUpCWXc3NzRVRkhuZ1p2MHVDVWlOV0hxTGlRVkd4ajhQZlNuZmFhWS81TE9NeWd6U1IrN0NrcWZyYzIzVElGb2Y3SlZoQ3A1NFJkRHhCV2lVSm9sb05lT3hIRVptd2syVjcvWEpFT1lTKzYiLCJtYWMiOiI3NDNlMDY4MGM5ODE1OTFiMzc2Nzg4ZDRjN2U0ZjJkZmFhZDI3NzU5ZTBiNTFjNzQyMmNhMTNkZWM4NmNlMTdlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-17******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17********\", {\"maxDepth\":0})</script>\n"}}