{"__meta": {"id": "X9d84986922a2f40b789c38326cbbe2d6", "datetime": "2025-06-16 08:50:54", "utime": **********.895987, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063853.501205, "end": **********.896023, "duration": 1.3948180675506592, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": 1750063853.501205, "relative_start": 0, "end": **********.722686, "relative_end": **********.722686, "duration": 1.2214810848236084, "duration_str": "1.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.722706, "relative_start": 1.221501111984253, "end": **********.896026, "relative_end": 2.86102294921875e-06, "duration": 0.17331981658935547, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45147136, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02858, "accumulated_duration_str": "28.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.803725, "duration": 0.02555, "duration_str": "25.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.398}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.855709, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.398, "width_percent": 4.409}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.873283, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.807, "width_percent": 6.193}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-143201216 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063844760%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ill6dURUVFdOVnowdkJ1KzhrVzN0b3c9PSIsInZhbHVlIjoibzN5VGZNV1ZNbjl2QnJMQjdNaU81R1I2bDdVVnA2c2VpQ3lHditPQlJ5RDRQSjNtd1VWd2Fxd3RHeE9Kb0NIM0xIRTBiQndRdVpaZk5sZFdNcVNLTHliOHhhc1NRLzYzSzQ3V1AvbGlSYlNkakhZbmd0emM5VkxvNUpOaS9OSGV2YWp0cDV4enk2VTMycTJSTGFTZWRYZHlaTkRpaTB2eTZjQjlkeTVZSzQvWGF1RXVhM3M5VXRMUVRHaVY1LzRqV2xDVUZrNzNncDUxcUl1WVFEM1Z4Zjlxc05oa0NmYjBKM2NxRHF1YWY0SFgwdG5oRmNaVmtnb3FVZmNER3ppcXR1aHRFUmIzMnN0ODBDbFVzRUdiNzRpb0FIemNXYWZtOEpsNktmaCt0M2tzSWNIaVhtUXB6T1lHSWp2ajZJMXppaXlHRjlKalFoOHFUMEZtNGFzc0lnTG9mZEFJS2d5eHZEaHlHVmk3VEdWN2hqS2ZqYzFjeHIxRWZXMUNVMzFKbW9vWE1CUU9CcDdUMitobDdwSytOWHZyT3V5eFJZSjBBMDRvRFVlUmdUNUpxTGNRS0hWcnQzYnoyaVVjMFVaekNHQ3BIWWZuaHNsZEQ1S0NPTXY4aUR3QVNkOXFDVmxGTnEzMENreDdlREdIUlhTVEs5LzU0Qlg2MzZUUGVQdHoiLCJtYWMiOiJiYzNmYWYzMjg5MDFjMWZmZmYyZjhhMWM0MzFmNDI1N2Q5MmQyZTg2ZTU2ODJjYzI2OTRlY2EyYWNiM2NkMTQzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImF2bHJZRHJiMkYxME1QUU02eUdCenc9PSIsInZhbHVlIjoiRjNVTnk4cmxJWW43YldGRUw1bkVHYm9lNlFGYlkxTjBBd2xvSnhjTGczZ20vbzFXTW9WeFpjbzU0QzBpc2grQW9ZWk5mTTBndUxCWHNLL2lQRjZLUmVlMmgxQlM2U3ZIZEFzTXd5WXpjRHVtL0ZrNHZMVmtZemptRURTYVI5UUdmb1plWkdnS0tYVnVwMzc1TWxhMG14MjdZcjhpb0szRXI5U29qT2V6SGVnOURaM3JBR0F3UzlRd25wTWhsdUpBRUtFWlpBR0N6dm1zSGR6Y3hiSlNZVUxpUTl6QnU3K2VuSFN5U1dXMUx0UzJxMm9od3JMNE1MbXhvSmdjNForTHNvdWx4MnplL1RsSjg5bjJKTHpVbGhtY3FKUkphbHFSUW03aWtmZllEYWxGVUIwU1BnNGxSWkRNVnlEZmE5SkJ3bTZGK3ExVFpRR0dZUXRwdWJUeFd5ZlhKaGlId2VsZ2tZNUM5Z29SZ2VhTEFZZGxKOFNyN0FaNGhjbWFFaTZuSmx0SWJzT2dRSFVHS1J0dVhoaHdGUTUxcm45K0FSekZtNUlpdnNDeWp2dHR2QnlYVkJ5VnBuZmg4NVRhZUFBU1NEL2RCVlJqN0k5Wlo2bVlGcHJiYkZOT2lLaS9udGpYME9mQk5sa3ZsTTRGYkppM0JOVWtKZ0QvWkk1eTRSN3EiLCJtYWMiOiIwODU0NjUyMmYzYjA1OTJkOGI4ZDVhYWRiNGZlMzk1MjA2YjgyNzAzNzU3MGJjNDAzNmI5ODQ2ODMxNmM4MjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143201216\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-202149363 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-202149363\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-190287157 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlPZEh6dGQxQ2ExZGR0c2Y1TGZCakE9PSIsInZhbHVlIjoiZG40Sy8xbUFHL0xLcUhNNnpVbFh4V0FWZ3YybnJoMlp4ZDZTYmtXM2FFTGJ2c1pKV0lBWEd3Zm1QTDRpWHNvWGxVemhhd09lOUtJemVXWUpjYUFhNDluRWl6S0htajR6dUtzSlpkNlFQbkpaTDZCdStwK3oxVVNETTJjL0VXRnVZc2IyZjBYdTd5VFVueHpVWlM2K0NReDNNcFlwN296aDh5VVYzL1NJODZWSmg5WUc2ajlJdDlRbHRYbC9Ra1VvQ040M3VUQ01QQnh2N0lrYTkxN05Fc1JCdXlBcGxUUXF3ZlVkdGJiT2p5SXN2N1VkRkVUblBTNzA0dkFnK2ZNblg2MlhFaWJDNWc3dFZ2RGs3VjQzS3hlR21kWm1XM1g0N25hYmt5cU1STVJ6UnBqQndrelVlUldpaFgrZW5LZ3J5aFZVQUdzWFdqVjhXekNheGQzUFYxL29nZGEzY1J5Ym5GeTRpQnlhWmZsUnNTa1Z3Yk9vYitlTW5YdHFmUXJNQ0xaU2thSTcrZDhQQzQ5enpaT0FlUGhPNkZqVmQrbnVvZ3JRWmVvdlhpVkZ4dUdkS1dzNlVSbVVzTVBVY2Z1RjVvYkZXU1hFMmxsamxybDM3ejU0TUloOUpnU1hFRnFiZ3FERyt4cmxuenQvcnZuRmxyNjhwalVvVnJVT2RoaEYiLCJtYWMiOiIxZTg5ZTNlMjZjMzczY2M3OTJjOGNmMWQxYTBiM2NiZDk5MGFmZjBlMzUyMTQxY2VhODI1YjY0YmU5YTA2ZmE0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InpuWUdrZXNlKzFKZ1Jzb0VqbGZkTVE9PSIsInZhbHVlIjoiRXdrWkRUWE1kVy90b0lpQzFuY0ZYRU1lUUduYXdCMW9uRm0wbmN3SG5lbHpnK01BRytzeVVkQmRXdXl5QWVKUytwcm5TN1kzTUcvM0FzRFFSaVlJdmF4RTdqVEt1WVI3cytoZWZ1dW5DQ0hFUUdHZFUyT2tNVTRrYzdpSmNQS2xDUDNvblFpN3lJaUlrSzlEZTliVEk1eGdBemF1WmM1OWk3VWs1WGFOei9kRDFuMWFDekQ5MUoxZHh0LzRueURJQWNyeHhVdmFHV09xNzFURm1MVyt2T014TzFBbTVNNVcwYzJBWG1TOEw4Q000dFF1S1EyY082bWhMUW5Sakw4TjRVb0hVZW9xTk5sbXpVQkhMM1JrMFJJdVE1T2tKaHNVYzlRWVVONUJZd1k4dGpYaDlKVnd5VDJvUmdMcGorczBLd2JNVENLQm9ZMHU0T3RUTmtTOExLS29xM1p1UDdVOE43dmg4bXRacG54c1JPMldra2tlN3h3bzZLbUJScE9EQitHS3BLT3VRYmEyTFZ4dGc3MTVMbTVDMnlNZUd5TWd6ZG9hcElpR3NrR2tCTXhrd2tHZEhWZmVuaVdUOVNGOUk1eEdkWENzY1JteEt5SUtON2hSbnRCSnFmQ2ZNNm02NU5BZ3pyNzVpbVl2NzJZbk1oemxvSVVCS04wNE5hTTQiLCJtYWMiOiI5NzliZTEzZjhjNDdhYzVlYzM5MzBhMDNmN2YwZDc5MmYzNGQyM2U4YjBhMjU5MWExMGJjNzNkNGEyOWMzMTU2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlPZEh6dGQxQ2ExZGR0c2Y1TGZCakE9PSIsInZhbHVlIjoiZG40Sy8xbUFHL0xLcUhNNnpVbFh4V0FWZ3YybnJoMlp4ZDZTYmtXM2FFTGJ2c1pKV0lBWEd3Zm1QTDRpWHNvWGxVemhhd09lOUtJemVXWUpjYUFhNDluRWl6S0htajR6dUtzSlpkNlFQbkpaTDZCdStwK3oxVVNETTJjL0VXRnVZc2IyZjBYdTd5VFVueHpVWlM2K0NReDNNcFlwN296aDh5VVYzL1NJODZWSmg5WUc2ajlJdDlRbHRYbC9Ra1VvQ040M3VUQ01QQnh2N0lrYTkxN05Fc1JCdXlBcGxUUXF3ZlVkdGJiT2p5SXN2N1VkRkVUblBTNzA0dkFnK2ZNblg2MlhFaWJDNWc3dFZ2RGs3VjQzS3hlR21kWm1XM1g0N25hYmt5cU1STVJ6UnBqQndrelVlUldpaFgrZW5LZ3J5aFZVQUdzWFdqVjhXekNheGQzUFYxL29nZGEzY1J5Ym5GeTRpQnlhWmZsUnNTa1Z3Yk9vYitlTW5YdHFmUXJNQ0xaU2thSTcrZDhQQzQ5enpaT0FlUGhPNkZqVmQrbnVvZ3JRWmVvdlhpVkZ4dUdkS1dzNlVSbVVzTVBVY2Z1RjVvYkZXU1hFMmxsamxybDM3ejU0TUloOUpnU1hFRnFiZ3FERyt4cmxuenQvcnZuRmxyNjhwalVvVnJVT2RoaEYiLCJtYWMiOiIxZTg5ZTNlMjZjMzczY2M3OTJjOGNmMWQxYTBiM2NiZDk5MGFmZjBlMzUyMTQxY2VhODI1YjY0YmU5YTA2ZmE0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InpuWUdrZXNlKzFKZ1Jzb0VqbGZkTVE9PSIsInZhbHVlIjoiRXdrWkRUWE1kVy90b0lpQzFuY0ZYRU1lUUduYXdCMW9uRm0wbmN3SG5lbHpnK01BRytzeVVkQmRXdXl5QWVKUytwcm5TN1kzTUcvM0FzRFFSaVlJdmF4RTdqVEt1WVI3cytoZWZ1dW5DQ0hFUUdHZFUyT2tNVTRrYzdpSmNQS2xDUDNvblFpN3lJaUlrSzlEZTliVEk1eGdBemF1WmM1OWk3VWs1WGFOei9kRDFuMWFDekQ5MUoxZHh0LzRueURJQWNyeHhVdmFHV09xNzFURm1MVyt2T014TzFBbTVNNVcwYzJBWG1TOEw4Q000dFF1S1EyY082bWhMUW5Sakw4TjRVb0hVZW9xTk5sbXpVQkhMM1JrMFJJdVE1T2tKaHNVYzlRWVVONUJZd1k4dGpYaDlKVnd5VDJvUmdMcGorczBLd2JNVENLQm9ZMHU0T3RUTmtTOExLS29xM1p1UDdVOE43dmg4bXRacG54c1JPMldra2tlN3h3bzZLbUJScE9EQitHS3BLT3VRYmEyTFZ4dGc3MTVMbTVDMnlNZUd5TWd6ZG9hcElpR3NrR2tCTXhrd2tHZEhWZmVuaVdUOVNGOUk1eEdkWENzY1JteEt5SUtON2hSbnRCSnFmQ2ZNNm02NU5BZ3pyNzVpbVl2NzJZbk1oemxvSVVCS04wNE5hTTQiLCJtYWMiOiI5NzliZTEzZjhjNDdhYzVlYzM5MzBhMDNmN2YwZDc5MmYzNGQyM2U4YjBhMjU5MWExMGJjNzNkNGEyOWMzMTU2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190287157\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}