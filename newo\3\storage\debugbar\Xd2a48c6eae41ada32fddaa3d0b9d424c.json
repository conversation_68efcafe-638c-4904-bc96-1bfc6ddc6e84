{"__meta": {"id": "Xd2a48c6eae41ada32fddaa3d0b9d424c", "datetime": "2025-06-16 08:50:29", "utime": **********.706676, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.293548, "end": **********.70671, "duration": 1.****************, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": **********.293548, "relative_start": 0, "end": **********.470325, "relative_end": **********.470325, "duration": 1.****************, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.470354, "relative_start": 1.****************, "end": **********.706712, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03124, "accumulated_duration_str": "31.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.572489, "duration": 0.02755, "duration_str": "27.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.188}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.630969, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.188, "width_percent": 5.57}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.681069, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.758, "width_percent": 6.242}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063818910%7C1%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iko4WmFUZi9zSzVneTNGeG9SQXNoT1E9PSIsInZhbHVlIjoiZXVaSW1HMkQzcVVNYXhIRmlBV0JYaEw0cC9kUmRxVVU0TUhUd3M4ZmJ0N0J2SFZ3Q2d1RXZrblZQQzVKZUNCZ1RnRWxZeTBnbHhod1hNcjY3K0hRbmVtRnBCallia05ZMWxIdG80RmtLeDhrL2tVenVTQm5ITjBrMHBNOWo5UnNYcmFUZmU5c1NuNXVaV1dzRTR6ZzFrNmxUV0hCK0JKZFk2aXlqY1BrMGZzTWhoUGlqRDk1TjAzb1JPOEI3N2didVVxaDl3MEc1VGdNY1cxNFJWMUJVbjNFYkJybWZaYXQ5a3NMK004a0UxQXVNa09uRmdFWDJ5U0ZTYlVlSkdPakgra2FjamhyOWdCdk4yVnB2ZUIzME80R2MzbXpqbHRibEpYcGVoa1ZmWHZ6azBSWktlWjlkQVF4WGh3NGJ6Q0hONWNpRWRIMnNMNVM4UE5JYmQvU2dqd1FhUWVLSkhoSHRrNVdLSWZGMkdnSGpsYTRleklmZkhPT29ySFpoZUtpbWx6Qys0TEhrWXFUSndTdE1wSzN0Y2Y2K3RsSWcxMGJoU1IyYnpBVkc5OEYxODh0WTlRakM1Y3F0cnVTaXdCZmFTOElYUTdiSitPeVE0a3lsRWJIZWxHemZ5M0VhL1dUb0ZhZCs2RjZyNkRIZUZnc3VFdDBTcjVxbzM3cUlHQXMiLCJtYWMiOiJkNzJmMzczNTg1ZTI5Y2M4ZTMxZDY5YjUzMmIyZTkzZmNmMjFmMDNhYTcwNzc5MjRkNGFmZjI2MGNiZDc4MjgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkYwMm13alZMMlBTcGJsSVhScUxqOHc9PSIsInZhbHVlIjoibk5CaDc5UUdXMVBSSjAvWWVkQWs3cEFROWNrdmVOd1dyTko3Uk9QT1g3NG5ma0FjakNDbENydmRRZ01zMDB6aTNPQU5pREZLd3hQSlczTTFiTzJlNDZ6TE5mWThKdVJ3M25wanR3Q1dJSmlRWmpWYTVGK3orRXlzMkRNangwVGh2L2RGVUg0dWVLUENhYUZoMSt4Rmh1aWhlSld4Nkl6NjFVWnBSSklDZkxSeUpQQnJjNm5mcXB5SW9rd0N1a3RvRXlMNmdDRlMrRmNRY2VCZjIwQ1VNN04rTUlTV1hnOWw1QnZVaDlUSmw5YjE3OTByaHp1cDk1ejlPRmdlaHlUMFhPOXpNckdTdjhuTWJCaW9tZmtLQyt4ZG5xazV2cTJoQmhiY0lpOEs4VWVFM05jZXd5Y2JvK3pkRkJYWWIvcmZodzRhU3pTTnk0aTNoaVRpa0VJY09SNHA5em9uOGxueklDNzhyWE9ZSGh5VElpU2MzY1ljRkx3VFlYZmhMNjBCY1hZc2ZINGdabnMrL3g5bGxVMHMwMUcvMWVjSkZmT2doMytaWU9rc2RRZEtkdGVnbXJYVkVQbE0wcFBBaUk0T205VXplZEEwMUp2MUdiWG1oNkg1SUtCNHFQdU1peWg1VE9xN0tZUUpKakhVK3dNRnByQXdYekNZNUJwRHYwRHoiLCJtYWMiOiI1MDdmYjkxZGQ1MTM1NWQzMzZjNTU4NzJmYTkyNjEwYWU1ZmFkY2I2Y2MwYjAyMzA5MDQ4ZDZhZmQ0NDZlZmU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-8085486 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xy8o1OLZEDaYyV6WX9XzzYzbw5zHFfm46SvuTNQ8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8085486\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1021575603 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlFSDk5YU90dFVzYTNkUkhmZlFkcUE9PSIsInZhbHVlIjoiSUI2R094a2tQczQycGpmb1NJZTFydEd4cUZLNXk1OFJVU2R5Z1FicVN2TDhxL3p3cmZRNEpUWURXdnNnbGFmSE5iZEk2aGdMeXluZkN6aGpLV00rTFJhcWMwVTRLeFlzVE5ZYStvWE9IdFgxRW9udThLckdtTnlySWM0Qm9pQi9NWjRpTTFmQWVyNk4reWRoRWlCV05EN1NJNjlxL2xBd1R4czQ1OUNjWE9HNCtTcVQzQmlhQ1Y3Mlc0ZWVQQU96bnNVQTdtZ1BWRExqZXo0OEozemlKSWJHV2hNelp0aWh5cndEcXhMVEhqaUpEZkhaVllmVCs1MnprMU9kZVZTaHRMVWJ0VmRka2ovY3RncjZBUjhwMEZLekdmbHVXc2E3RmE3SVoxZHVGTjJZYVBXcHRPZkdyMHFRMlF6NVFFSDFKU1g3S2FvdkxzNW8yT2hZZzVNOXZwYTJBMDVvUWoraElUYU9waUNpOHFHWU1zVzNxSUdlMHYrckJWd3Nsck5KS1c0STFQd1krOXd0L24xTVJRa0xmejJLczMzVDNHcElXZkcxbSsrYjhmYXNmb0x3Q0tRL0U5OEQ5Rml6Q2VHYjQwQ21VOGwxM1NSQVM4OHVxWlNNd2gyazc4eW5kSE9aMW1Qb3haOXB1WHZOTXBsQ1d5anJVVkN1WkFPNlYwZWMiLCJtYWMiOiJkZmI5ZGQ5MDA5ZTAyYzQ5MzU3NmY5YjRiMzZhMjQ2ZDgzN2RhMGFmNmI5MWI4NDc4NWI3OWRhNmJiNWExMjA1IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1sNU5JM080bXdWVW5na25jbm94Q1E9PSIsInZhbHVlIjoiV1M5SmZHdnZlNVY1ck1YWGxGdHFhTkxya3FrWEFHRW9HY0VpWmRSdU1OcGJNYXRRZHBJN05iTHVzL1g2QXRDSk4zZmdoRTZCK2tJUE9zMi9OOUN2MDhvZXhXVFJSR0ROUWswcDNjVEgvN0M5MERuY01qeFUvSCszNlVvOGZBUkd1NEV0MDFBK0FxTDVQVkNjbXZmL0pEYlRIL3JyaUNsbXRMRDNDZyt6dVhyZUlpZkFnbU95NzRQSE9tbGFWMWQ5ODJmMlF3RWpKNmVrdjhuZlhuRnFwSU8reWgvRDZEa0ZZczhXZmlGc2tOWHZhZEVoMFlZcU1iaTVmbjFqL3AvMHlSa0YxQ3YyMWFsaTZpbHhuaHhidlZjVnJDM1JIcjkxeWJYTjBocFZJNkc4M3FhdHN3MVRUOHJxNUM3c2lac1pBWXFVOHNYNE56WWhDZzBaS0picjE1TTJqZm50d2R5U1ZldEkrNjIyUnc3cjY4VWNrVnQvb2paTTNIdFVFeVZ2ZkdSblpIZ1BqS1g5eVRiNGk5QnBoTlkrSFRkVVFnYVg0elVIWUE4c0NpZnl2UEIwNENTTkRiOS9EN0V5ekFvNmdING5tNGFTMEVJZFJwSEVWTkRKRW1rdEZpbFRtazVxUzV3c05DN1FFekNzb3ZwUCsybWgxSWoyeWVuSUgxSmoiLCJtYWMiOiI3ODEwNTRiMTM0NDgwYzJjYTljZmVjYmUwYTI2MmZlOGI4ZDZkMGRiYzdmODY1MDkxYzRmMWFkYTdhZjcxMDZhIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlFSDk5YU90dFVzYTNkUkhmZlFkcUE9PSIsInZhbHVlIjoiSUI2R094a2tQczQycGpmb1NJZTFydEd4cUZLNXk1OFJVU2R5Z1FicVN2TDhxL3p3cmZRNEpUWURXdnNnbGFmSE5iZEk2aGdMeXluZkN6aGpLV00rTFJhcWMwVTRLeFlzVE5ZYStvWE9IdFgxRW9udThLckdtTnlySWM0Qm9pQi9NWjRpTTFmQWVyNk4reWRoRWlCV05EN1NJNjlxL2xBd1R4czQ1OUNjWE9HNCtTcVQzQmlhQ1Y3Mlc0ZWVQQU96bnNVQTdtZ1BWRExqZXo0OEozemlKSWJHV2hNelp0aWh5cndEcXhMVEhqaUpEZkhaVllmVCs1MnprMU9kZVZTaHRMVWJ0VmRka2ovY3RncjZBUjhwMEZLekdmbHVXc2E3RmE3SVoxZHVGTjJZYVBXcHRPZkdyMHFRMlF6NVFFSDFKU1g3S2FvdkxzNW8yT2hZZzVNOXZwYTJBMDVvUWoraElUYU9waUNpOHFHWU1zVzNxSUdlMHYrckJWd3Nsck5KS1c0STFQd1krOXd0L24xTVJRa0xmejJLczMzVDNHcElXZkcxbSsrYjhmYXNmb0x3Q0tRL0U5OEQ5Rml6Q2VHYjQwQ21VOGwxM1NSQVM4OHVxWlNNd2gyazc4eW5kSE9aMW1Qb3haOXB1WHZOTXBsQ1d5anJVVkN1WkFPNlYwZWMiLCJtYWMiOiJkZmI5ZGQ5MDA5ZTAyYzQ5MzU3NmY5YjRiMzZhMjQ2ZDgzN2RhMGFmNmI5MWI4NDc4NWI3OWRhNmJiNWExMjA1IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1sNU5JM080bXdWVW5na25jbm94Q1E9PSIsInZhbHVlIjoiV1M5SmZHdnZlNVY1ck1YWGxGdHFhTkxya3FrWEFHRW9HY0VpWmRSdU1OcGJNYXRRZHBJN05iTHVzL1g2QXRDSk4zZmdoRTZCK2tJUE9zMi9OOUN2MDhvZXhXVFJSR0ROUWswcDNjVEgvN0M5MERuY01qeFUvSCszNlVvOGZBUkd1NEV0MDFBK0FxTDVQVkNjbXZmL0pEYlRIL3JyaUNsbXRMRDNDZyt6dVhyZUlpZkFnbU95NzRQSE9tbGFWMWQ5ODJmMlF3RWpKNmVrdjhuZlhuRnFwSU8reWgvRDZEa0ZZczhXZmlGc2tOWHZhZEVoMFlZcU1iaTVmbjFqL3AvMHlSa0YxQ3YyMWFsaTZpbHhuaHhidlZjVnJDM1JIcjkxeWJYTjBocFZJNkc4M3FhdHN3MVRUOHJxNUM3c2lac1pBWXFVOHNYNE56WWhDZzBaS0picjE1TTJqZm50d2R5U1ZldEkrNjIyUnc3cjY4VWNrVnQvb2paTTNIdFVFeVZ2ZkdSblpIZ1BqS1g5eVRiNGk5QnBoTlkrSFRkVVFnYVg0elVIWUE4c0NpZnl2UEIwNENTTkRiOS9EN0V5ekFvNmdING5tNGFTMEVJZFJwSEVWTkRKRW1rdEZpbFRtazVxUzV3c05DN1FFekNzb3ZwUCsybWgxSWoyeWVuSUgxSmoiLCJtYWMiOiI3ODEwNTRiMTM0NDgwYzJjYTljZmVjYmUwYTI2MmZlOGI4ZDZkMGRiYzdmODY1MDkxYzRmMWFkYTdhZjcxMDZhIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1021575603\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-7******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-7********\", {\"maxDepth\":0})</script>\n"}}