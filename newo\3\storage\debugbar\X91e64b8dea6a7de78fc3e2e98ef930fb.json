{"__meta": {"id": "X91e64b8dea6a7de78fc3e2e98ef930fb", "datetime": "2025-06-16 08:51:20", "utime": 1750063880.033608, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063878.652077, "end": 1750063880.033639, "duration": 1.3815619945526123, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1750063878.652077, "relative_start": 0, "end": **********.83701, "relative_end": **********.83701, "duration": 1.1849329471588135, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.837037, "relative_start": 1.184960126876831, "end": 1750063880.033642, "relative_end": 3.0994415283203125e-06, "duration": 0.19660496711730957, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45147088, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021979999999999996, "accumulated_duration_str": "21.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.939859, "duration": 0.019649999999999997, "duration_str": "19.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.399}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.989181, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.399, "width_percent": 5.05}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750063880.009695, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.449, "width_percent": 5.551}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1448496602 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1448496602\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1321379378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321379378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-990817999 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-990817999\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-167531467 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063854453%7C4%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNVc0dwK0JDT2cxZjY1ZGQ4ZG1Yc3c9PSIsInZhbHVlIjoiMzRqOWdHaThoUkRqQlhYdzdpbUUwcEJFRm1sSVh2Uld5dkxEN3Q0ZkgyZ3d5MExsUWRibi9OWmlsSGFHTGFEcWtvZGtrVFM0NzJJdS9qZ2Nhand5ODdSb0tKOTYxM3pWd1dIK0dNekw0dldwSVRnMmQwaWJRYWtNcHE4ZnJyK2g0NmJPeEhzOXZkZklTVkl0MHkxNkF1eGNZbEpjQnI4d2ZiKzNEd2E5eXNKTkRzWExwQ0h4c3dxVE14NzFvVWRRa0p6amphQ1VZdSsyNkt6SE1IdU1XSm1LQ1VBMlRTWWRydUdUclc0UFRkZmJDaTZMWmo2cXFTRTVjS1RIRk1Wc0VtZWttVTVwRHFqbUsrcDJhRDNBTkg1d3ZZaTZGRlFJaVFJWGFVZDQ2QnVLejFnU3lJUWF2UlJQbmlXVlg1c0VGSDZmMzNNam1UemFwRkk1d29XZmdBMW5pb0lZSXdJUGYySTQ4M0tMeFgrakFLdC9MbHN2YXovMVZuSGNRdGNmRzlBR2hVWmdaQnA1TEN6cWhNaGpxazhGY3JlSVZ3RVFNcjR3M0FsaTEzcnB5NThIbjRGNmZCTW55LzJMZlhNeUpTUVdxUFN0eHI2UnREc2QyMVhpNVFJSTFjTW5NK0lCTU9GUWtCSlUrbjhUbkhPV2h5TzlNUFExUENYb0EyUU4iLCJtYWMiOiI4ZWNjOGZjMGUxNWI0MDU4M2U1YTgxZmRkMDEwNTNjYjE5ZjYzYzVkZmM4YTQ2YjA1OTI4ODZlYzkwMTUzNmU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJINDJPbFZNNldEZXhnQ2FHL2g3WHc9PSIsInZhbHVlIjoibjFud2hVTEdUOEJYSWNKUmpSVDBYQlJlSDE1YkYwdlRIaEpCVWZqNFN6NXgvVzhaZk1LM0tydGlCRUlKWGVyck95Z1JwcFpkcDVwWTVpLzZJY2g3Y1BZbmlUcHJyamVpNE1Bbzd2NXRNK0U2U2pidjZVRjk1d1RMSVkvN2w1bTRqK3FrK01XYVNWbmVyeWhoVWtlbG5iaWxqdThHS3Y3NU9wRHpicm91MFZmT0VkOGpIOWdWaE5aWkpRYmlObjNpcXIxMk8wa01uZ1F0bVN1SnhYM04zVmlXY3lIYkZNekJQQXFTeW5Hc2t0bFg5ZVhDTDhmYy81QzE0MzUxekpyQ1BGQnZ6ZHhlVXEyZWI5SEtpb2hRTDhwTWg0ZUhYcTlxekZUWHBHYjA4QkswOHJhWVcyM21xUXBPOUNMa1hKN3ZCUW9La0IrTDB5dk5QMk5ZK1BpdkE3dlN6WHlLZDNFMmZwY0NuZ21zSzBrK2hmeEhBR1kyUnJaTDBzNXptamlpdG5GamlOSXBLL0ZHUGJCTGg2UzZOZEdKVkVEVkgyMzczSzdtT3E5dGRZdkNGSnhHVUdGRzhOZ3VZVERUSktYM0dCVkFTRlp2cUZnb3FBWmhFQ2o4alRnamY0RmNZc0Y1UlJiZWFyNTBzM0h5Tnc1c3UwcnpHYWEyM05WbjRuSnoiLCJtYWMiOiIzNGUxZWNkNzM1YmZkYzIzOTIxMTFjODFhY2ZiMzRhYjM1ZjliYzQzODQ3NDBjZDBlNWFlOGZkMzczZTViY2UyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-167531467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-770174128 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770174128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-305119953 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:51:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZzdG5EM1dBZEhNdWFHVUhPVlZWTWc9PSIsInZhbHVlIjoiSjhqZmhneFptNlVhd0gzbXk3bnEwdHpGMndHNVpncHdCa0VJc21ZL09RS0Q0UDhQYnRhM21zSmZKRnkyT3JqUElSRXNKRStJUU55UWJDbUk5aFZ1a3RrOGVORERSMS9EQk1yN3pWYks5T0k2cGxrTmZZMnd6bkZ0MHZpRWhJUm53Q1cxN1R6WGtiMXZkbkpPZWROWVFhYzdjQkZOV0p3WWFUS0I5Q0VlQzN0SzBHdHFBM3NKeGdEdjZ1VDRrd0lzUnFwTVV1ajhEV28rR0RzRHRGaTFsbXZzWDY5NHNDbi9reERjNlB6ZFNNM3A1b2NxNmVpQ1ZQL00ydm9PMytlQWhmdy9LRC9BWWJheDRZOWFqNjZ3REg3MXJPbWQ3MWpoUGRlWTZHVHVkcTlzT0NhN2daV0RjMEFvWldZNDdRS2ZlWEFPdjN3a2IxaGhUZ000QzhDT0QvVXAwWWhpVzdCS29SM2kxbFd5RzUvMzhuSHExYzBrTmN0Zk5zVzZLY1duUnNTSDJuNVBqU0Zod2E2a0wycmpRaGEwUWROR1lYdlNWUXM5VnVtVTJuRWk2QS8wMElKcVdHSzJkenJhMWxrYURiNlgrN040WTh0UzNGOVRUZTZOeW9LWitnRG1tV1lTUUtaY3lZMEhJaUt1b2RvL1EwTnM4V01KbHQ3ZkcxRkUiLCJtYWMiOiJiNjRhYjVhNmZmNjQyNDlmYTkwOTM3MDc3NDRhZDM1YzBhYjU1YzIwZTYyMTBlNjFjNDEzYmNjNWNlZTdhNmUzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InRGczlyTXMybk1LN29BTXJ0RmFEbWc9PSIsInZhbHVlIjoiRDhDU3VOalIxUXZFVmZtY2piUU9QV3lqaUdDdVgza2NDb1M1YWN5VW9NNnY0NFJQUkJhem9mQVZWckFySXlaZ01kV1p3QlZCRkFEN3B5S092WERjajNPTHRrUUpnanRMcmdUUG5GRFdMTW1uN3JSQnF6czlsOFc0NnNuTFppQy9WWlRpejlHK0FRc3RlKzAraWh6Y0lVcXZmanJQQzBDd21LN2ZXWXo3Mk1RYTVtcDJaUjdkMkx4Nm5qYlJscFE5c1pZWFlLcHlwRThvZExmZUI4QlliY3BMdXhkd1hYckxFZTJQdkVSZEtUUktCMWZJQVZuZTJ0ZmhVclBzQ3RNZ3hCT3ErVUhNSVpUTGovWXp1ZE9sV2ZPRFBNZ25QK1BTUHJ1UFVFWm5mTW9ObVNHUDl1NVBOaHROT2J2cjhtMHRBR0h5MTUyTGF5T2wxMG9pamU2aURQUlFWUjhFRkswZ1pQSjFIeE5qMUtpeEYxc0lYMlV2bEs2WVBocjRpMi9nblVhS2dPYk9zNjZtUDE0K0FlVEpSRTRzU092cUt6TitqN3ROVzgvSmp5OXN5K0NxSFlOODhLOVVwaFRKYnl1Yk90TFU3ZnNMeTgvNWN1ME5tUlpaWEo2cTFMSGdBN29SSm1xd0hPcXJkZE1TeWpFNlFiaHQxL3VYREVXaDFSbFUiLCJtYWMiOiIxODZkZTU3YzE3ODM3NjA1YzRlNDcwYjRhM2U1MjNiNTI4ZTYyMjQ0Njg4YjgwMWQwMWY2MTFlZWIzMTZhOWJjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZzdG5EM1dBZEhNdWFHVUhPVlZWTWc9PSIsInZhbHVlIjoiSjhqZmhneFptNlVhd0gzbXk3bnEwdHpGMndHNVpncHdCa0VJc21ZL09RS0Q0UDhQYnRhM21zSmZKRnkyT3JqUElSRXNKRStJUU55UWJDbUk5aFZ1a3RrOGVORERSMS9EQk1yN3pWYks5T0k2cGxrTmZZMnd6bkZ0MHZpRWhJUm53Q1cxN1R6WGtiMXZkbkpPZWROWVFhYzdjQkZOV0p3WWFUS0I5Q0VlQzN0SzBHdHFBM3NKeGdEdjZ1VDRrd0lzUnFwTVV1ajhEV28rR0RzRHRGaTFsbXZzWDY5NHNDbi9reERjNlB6ZFNNM3A1b2NxNmVpQ1ZQL00ydm9PMytlQWhmdy9LRC9BWWJheDRZOWFqNjZ3REg3MXJPbWQ3MWpoUGRlWTZHVHVkcTlzT0NhN2daV0RjMEFvWldZNDdRS2ZlWEFPdjN3a2IxaGhUZ000QzhDT0QvVXAwWWhpVzdCS29SM2kxbFd5RzUvMzhuSHExYzBrTmN0Zk5zVzZLY1duUnNTSDJuNVBqU0Zod2E2a0wycmpRaGEwUWROR1lYdlNWUXM5VnVtVTJuRWk2QS8wMElKcVdHSzJkenJhMWxrYURiNlgrN040WTh0UzNGOVRUZTZOeW9LWitnRG1tV1lTUUtaY3lZMEhJaUt1b2RvL1EwTnM4V01KbHQ3ZkcxRkUiLCJtYWMiOiJiNjRhYjVhNmZmNjQyNDlmYTkwOTM3MDc3NDRhZDM1YzBhYjU1YzIwZTYyMTBlNjFjNDEzYmNjNWNlZTdhNmUzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InRGczlyTXMybk1LN29BTXJ0RmFEbWc9PSIsInZhbHVlIjoiRDhDU3VOalIxUXZFVmZtY2piUU9QV3lqaUdDdVgza2NDb1M1YWN5VW9NNnY0NFJQUkJhem9mQVZWckFySXlaZ01kV1p3QlZCRkFEN3B5S092WERjajNPTHRrUUpnanRMcmdUUG5GRFdMTW1uN3JSQnF6czlsOFc0NnNuTFppQy9WWlRpejlHK0FRc3RlKzAraWh6Y0lVcXZmanJQQzBDd21LN2ZXWXo3Mk1RYTVtcDJaUjdkMkx4Nm5qYlJscFE5c1pZWFlLcHlwRThvZExmZUI4QlliY3BMdXhkd1hYckxFZTJQdkVSZEtUUktCMWZJQVZuZTJ0ZmhVclBzQ3RNZ3hCT3ErVUhNSVpUTGovWXp1ZE9sV2ZPRFBNZ25QK1BTUHJ1UFVFWm5mTW9ObVNHUDl1NVBOaHROT2J2cjhtMHRBR0h5MTUyTGF5T2wxMG9pamU2aURQUlFWUjhFRkswZ1pQSjFIeE5qMUtpeEYxc0lYMlV2bEs2WVBocjRpMi9nblVhS2dPYk9zNjZtUDE0K0FlVEpSRTRzU092cUt6TitqN3ROVzgvSmp5OXN5K0NxSFlOODhLOVVwaFRKYnl1Yk90TFU3ZnNMeTgvNWN1ME5tUlpaWEo2cTFMSGdBN29SSm1xd0hPcXJkZE1TeWpFNlFiaHQxL3VYREVXaDFSbFUiLCJtYWMiOiIxODZkZTU3YzE3ODM3NjA1YzRlNDcwYjRhM2U1MjNiNTI4ZTYyMjQ0Njg4YjgwMWQwMWY2MTFlZWIzMTZhOWJjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305119953\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-868383481 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868383481\", {\"maxDepth\":0})</script>\n"}}