{"__meta": {"id": "X61f38e5cae9040adecfd062c0a66967f", "datetime": "2025-06-16 08:51:20", "utime": 1750063880.035335, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063878.655996, "end": 1750063880.035365, "duration": 1.3793690204620361, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1750063878.655996, "relative_start": 0, "end": **********.833334, "relative_end": **********.833334, "duration": 1.177337884902954, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.833355, "relative_start": 1.177358865737915, "end": 1750063880.035368, "relative_end": 2.86102294921875e-06, "duration": 0.2020130157470703, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45164112, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024749999999999998, "accumulated_duration_str": "24.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9371831, "duration": 0.022269999999999998, "duration_str": "22.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.98}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9892712, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.98, "width_percent": 4.808}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750063880.010392, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.788, "width_percent": 5.212}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1361727106 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1361727106\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1602697702 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1602697702\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1648384152 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648384152\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-24980118 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063854453%7C4%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNVc0dwK0JDT2cxZjY1ZGQ4ZG1Yc3c9PSIsInZhbHVlIjoiMzRqOWdHaThoUkRqQlhYdzdpbUUwcEJFRm1sSVh2Uld5dkxEN3Q0ZkgyZ3d5MExsUWRibi9OWmlsSGFHTGFEcWtvZGtrVFM0NzJJdS9qZ2Nhand5ODdSb0tKOTYxM3pWd1dIK0dNekw0dldwSVRnMmQwaWJRYWtNcHE4ZnJyK2g0NmJPeEhzOXZkZklTVkl0MHkxNkF1eGNZbEpjQnI4d2ZiKzNEd2E5eXNKTkRzWExwQ0h4c3dxVE14NzFvVWRRa0p6amphQ1VZdSsyNkt6SE1IdU1XSm1LQ1VBMlRTWWRydUdUclc0UFRkZmJDaTZMWmo2cXFTRTVjS1RIRk1Wc0VtZWttVTVwRHFqbUsrcDJhRDNBTkg1d3ZZaTZGRlFJaVFJWGFVZDQ2QnVLejFnU3lJUWF2UlJQbmlXVlg1c0VGSDZmMzNNam1UemFwRkk1d29XZmdBMW5pb0lZSXdJUGYySTQ4M0tMeFgrakFLdC9MbHN2YXovMVZuSGNRdGNmRzlBR2hVWmdaQnA1TEN6cWhNaGpxazhGY3JlSVZ3RVFNcjR3M0FsaTEzcnB5NThIbjRGNmZCTW55LzJMZlhNeUpTUVdxUFN0eHI2UnREc2QyMVhpNVFJSTFjTW5NK0lCTU9GUWtCSlUrbjhUbkhPV2h5TzlNUFExUENYb0EyUU4iLCJtYWMiOiI4ZWNjOGZjMGUxNWI0MDU4M2U1YTgxZmRkMDEwNTNjYjE5ZjYzYzVkZmM4YTQ2YjA1OTI4ODZlYzkwMTUzNmU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImJINDJPbFZNNldEZXhnQ2FHL2g3WHc9PSIsInZhbHVlIjoibjFud2hVTEdUOEJYSWNKUmpSVDBYQlJlSDE1YkYwdlRIaEpCVWZqNFN6NXgvVzhaZk1LM0tydGlCRUlKWGVyck95Z1JwcFpkcDVwWTVpLzZJY2g3Y1BZbmlUcHJyamVpNE1Bbzd2NXRNK0U2U2pidjZVRjk1d1RMSVkvN2w1bTRqK3FrK01XYVNWbmVyeWhoVWtlbG5iaWxqdThHS3Y3NU9wRHpicm91MFZmT0VkOGpIOWdWaE5aWkpRYmlObjNpcXIxMk8wa01uZ1F0bVN1SnhYM04zVmlXY3lIYkZNekJQQXFTeW5Hc2t0bFg5ZVhDTDhmYy81QzE0MzUxekpyQ1BGQnZ6ZHhlVXEyZWI5SEtpb2hRTDhwTWg0ZUhYcTlxekZUWHBHYjA4QkswOHJhWVcyM21xUXBPOUNMa1hKN3ZCUW9La0IrTDB5dk5QMk5ZK1BpdkE3dlN6WHlLZDNFMmZwY0NuZ21zSzBrK2hmeEhBR1kyUnJaTDBzNXptamlpdG5GamlOSXBLL0ZHUGJCTGg2UzZOZEdKVkVEVkgyMzczSzdtT3E5dGRZdkNGSnhHVUdGRzhOZ3VZVERUSktYM0dCVkFTRlp2cUZnb3FBWmhFQ2o4alRnamY0RmNZc0Y1UlJiZWFyNTBzM0h5Tnc1c3UwcnpHYWEyM05WbjRuSnoiLCJtYWMiOiIzNGUxZWNkNzM1YmZkYzIzOTIxMTFjODFhY2ZiMzRhYjM1ZjliYzQzODQ3NDBjZDBlNWFlOGZkMzczZTViY2UyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24980118\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-726920381 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726920381\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1472575075 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:51:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFkbzQ2QVVKcldUTk4wWldrSnlsRGc9PSIsInZhbHVlIjoieUZ3VGNQNXMxVmcwb3UydFZmY25Ja09UcUhyR2tVd2NmeVorMUw1QmVZZWt4ZVhtMVVYU0c5cCtiTXZTWUExNHUzQlVlWDVCYTliL29ya3pvbnVvTGtxU01xYVR4a1J5R1ZkNTlHbFZqK1JKVGExcWpScFRmdWRYOVFLS0F0Zm1Kbll6S0R1aHR4akJaQUZUSmkrS2ZkMktHdlZHL2RPQ3JXQ2daVkFxVW4zSkljcDJtNzBQendpWXZrWXJwYm1abEJYZWY4cDR3R25DRVFhUkxxMFlIVDk1ZW5oQnF3Y3FLN0lEKytJQ29vNTNyYWM3LzZDMnNLb0M3SjA2QVNQaGZuNlp0OTZGbGN4ZElkVFFKaThMTGxnR0cwY2xsTVBUbTh6bytIWXRBYkNqZUZUQncxQnZNWWhWbjdXK0ZGbEpMZzhYVDZVa1NYWUg5eEoyay91TmF2VXFtRDdPMHNvK212aGZRdlZnaWcxZkMwaDg1YXFNWWZOR1AycnREaUFlbDNKaXlURFhmTktpTjFLUGJHdmJBYkVMQWRob0pUTzJma2RBWXpxRnRMYVErT25yNXkvdUtTOXdpTUNwY0dGUzVnbmw0YTFSUW5uek9oNWR2N2lCRlpzOStyRDMraVRxdTFiSlVlVXN0VDAvb1UzMjJEdjM0QXJzQ2I0N3Q4T3QiLCJtYWMiOiJmMDNiOTU2ZmUxNDY2ZGIyOWEwMmU0MDg0MGQ2YjJiY2Q0N2UwNjBiNjc3YWQzMDhiYmJjMzQyODU4YzVlNmNlIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Inp1bm43dlJqMlRQLzNNR0pPR3hUOGc9PSIsInZhbHVlIjoiOG9GVkVvUzR5YmxGeHVndWt3NHZsblErVk1acjhwN2NxbllIeVZJdE5nS2w1N0pxLzZiQlY1NHVic3hxOE01SjBtbjhPUEdFUzc4OWRwYjF6d2VhdHM5a2M1K3dTMHUxK1lpbm1ydnlKR3IzU3g1WUpyRDFmZzJmc0loSWpxeGFsdDM3c0NOWWh1ays2TXNaZ0F6RmJrdmlsQkdLTGZ1THM3ZjA2MUpXMUhXdDU3VkMvNU5ldTlETUIrNWlBVFdVaXIyRFFPa1M3b01MZzFNa0JzV3UvQmc3Z1gvZ21OWWh2RjJEbXpVcXFaTzAvc29ibzg5SVJYNktMUUxybW8xcTAySUNpVEhqT0ZwM1Y5Tk5veXlSbHRCVW9IVVRSVU5JTmMwTXBTNWUxUkZNRnNsdDhVb0tQdG9jc1ZhZFNLNHRVdkdzclBETVdEckRpUTNRMUhFNlBDYUM0ZldIdzdsYUErUmdEQ0JwVlQraFI1aVBpZENIOWVpMmVIVkpONXhNOXhhR2ZqMHNMdjljR0x6WllUcmR3YmwwMkJwVTFnUVA0QldDVmpnR25mOWpzZ2dBMUxmRE0rUkZmaVNpb0RWc3h2S0NnNGZOUGg3T3h5eDhseGZ2blAxbDNmdi82M3ZpZlV1bHJyNjlKNDhEeWlXaHhQN2Vza01PWEF6WExNdFEiLCJtYWMiOiJmOTdlZmQ3YzYzZjAzYmQwMjg5YWFiYjg0ZGQzMjgzNmU2NTUzNjIxMDQwMDg4ZGJlOTU0MGY1ZjBjNWU4NjEzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:51:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFkbzQ2QVVKcldUTk4wWldrSnlsRGc9PSIsInZhbHVlIjoieUZ3VGNQNXMxVmcwb3UydFZmY25Ja09UcUhyR2tVd2NmeVorMUw1QmVZZWt4ZVhtMVVYU0c5cCtiTXZTWUExNHUzQlVlWDVCYTliL29ya3pvbnVvTGtxU01xYVR4a1J5R1ZkNTlHbFZqK1JKVGExcWpScFRmdWRYOVFLS0F0Zm1Kbll6S0R1aHR4akJaQUZUSmkrS2ZkMktHdlZHL2RPQ3JXQ2daVkFxVW4zSkljcDJtNzBQendpWXZrWXJwYm1abEJYZWY4cDR3R25DRVFhUkxxMFlIVDk1ZW5oQnF3Y3FLN0lEKytJQ29vNTNyYWM3LzZDMnNLb0M3SjA2QVNQaGZuNlp0OTZGbGN4ZElkVFFKaThMTGxnR0cwY2xsTVBUbTh6bytIWXRBYkNqZUZUQncxQnZNWWhWbjdXK0ZGbEpMZzhYVDZVa1NYWUg5eEoyay91TmF2VXFtRDdPMHNvK212aGZRdlZnaWcxZkMwaDg1YXFNWWZOR1AycnREaUFlbDNKaXlURFhmTktpTjFLUGJHdmJBYkVMQWRob0pUTzJma2RBWXpxRnRMYVErT25yNXkvdUtTOXdpTUNwY0dGUzVnbmw0YTFSUW5uek9oNWR2N2lCRlpzOStyRDMraVRxdTFiSlVlVXN0VDAvb1UzMjJEdjM0QXJzQ2I0N3Q4T3QiLCJtYWMiOiJmMDNiOTU2ZmUxNDY2ZGIyOWEwMmU0MDg0MGQ2YjJiY2Q0N2UwNjBiNjc3YWQzMDhiYmJjMzQyODU4YzVlNmNlIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Inp1bm43dlJqMlRQLzNNR0pPR3hUOGc9PSIsInZhbHVlIjoiOG9GVkVvUzR5YmxGeHVndWt3NHZsblErVk1acjhwN2NxbllIeVZJdE5nS2w1N0pxLzZiQlY1NHVic3hxOE01SjBtbjhPUEdFUzc4OWRwYjF6d2VhdHM5a2M1K3dTMHUxK1lpbm1ydnlKR3IzU3g1WUpyRDFmZzJmc0loSWpxeGFsdDM3c0NOWWh1ays2TXNaZ0F6RmJrdmlsQkdLTGZ1THM3ZjA2MUpXMUhXdDU3VkMvNU5ldTlETUIrNWlBVFdVaXIyRFFPa1M3b01MZzFNa0JzV3UvQmc3Z1gvZ21OWWh2RjJEbXpVcXFaTzAvc29ibzg5SVJYNktMUUxybW8xcTAySUNpVEhqT0ZwM1Y5Tk5veXlSbHRCVW9IVVRSVU5JTmMwTXBTNWUxUkZNRnNsdDhVb0tQdG9jc1ZhZFNLNHRVdkdzclBETVdEckRpUTNRMUhFNlBDYUM0ZldIdzdsYUErUmdEQ0JwVlQraFI1aVBpZENIOWVpMmVIVkpONXhNOXhhR2ZqMHNMdjljR0x6WllUcmR3YmwwMkJwVTFnUVA0QldDVmpnR25mOWpzZ2dBMUxmRE0rUkZmaVNpb0RWc3h2S0NnNGZOUGg3T3h5eDhseGZ2blAxbDNmdi82M3ZpZlV1bHJyNjlKNDhEeWlXaHhQN2Vza01PWEF6WExNdFEiLCJtYWMiOiJmOTdlZmQ3YzYzZjAzYmQwMjg5YWFiYjg0ZGQzMjgzNmU2NTUzNjIxMDQwMDg4ZGJlOTU0MGY1ZjBjNWU4NjEzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:51:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1472575075\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}