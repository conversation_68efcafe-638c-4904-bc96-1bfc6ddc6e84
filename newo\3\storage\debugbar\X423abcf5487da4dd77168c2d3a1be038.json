{"__meta": {"id": "X423abcf5487da4dd77168c2d3a1be038", "datetime": "2025-06-16 08:50:46", "utime": **********.953577, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.567962, "end": **********.95361, "duration": 1.****************, "duration_str": "1.39s", "measures": [{"label": "Booting", "start": **********.567962, "relative_start": 0, "end": **********.746919, "relative_end": **********.746919, "duration": 1.****************, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.746941, "relative_start": 1.****************, "end": **********.953613, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023960000000000002, "accumulated_duration_str": "23.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.832751, "duration": 0.0206, "duration_str": "20.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.977}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8828871, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.977, "width_percent": 5.551}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9299302, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 91.528, "width_percent": 8.472}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063844760%7C3%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNVdzZCWDNUaVJVanZYd3NRalVVc1E9PSIsInZhbHVlIjoiWlpDL0MweEdCMjh0WFcwZzJpTDdXTk1RQWJkSVpxNzBSOXM3ZU5IeXN5UE10RzZ3ejkrWnVMQzBGS1JiekQ5R0hPWTNaU0liSXl5M2JxS213Y3hFamFSUlhtaVU2YlZuNmlHTlFVcTdDRDFwaFNnZFhDblQ0anA1VE1FbGZRZ3lmS3N1Z1BTS0kzUG5DQmpMWWt1ekd4UHN1cktXTnJyaU9Pa1VWWEJXdkVXWmViSnh5L2s3amRreGlaKzlCRmt6V0RiNlNadjJHdk96d3M1Qk82MnFHM3oxUmZHL0NoR1lGTk9DbVlQYmdwekhJSWs3NCtUMjEwM2NQQlpKOE9UdUpNTExLMkxXc20vMW51QW9LVFlyalV1Nm1iTDVnWlVsekRZSEcwNUFsQ21CTHBoblJPSVpIV2RsbEc1RERhaVpHMmZxWFRPM1NlVHBoY2tRNndQNW9QTys3TFd1Vk51S1VHNmZzSFdaQlFXa3hsN045Sy9kZ3QyTk53L3lVVmdsZG9CZFgwYjJIUTFnUlc3WE9vUjVCYkhHR3QxcjI0MjJYL1RGUytJQmZmRE9abWFTOEdGZlp2dGo0bVdTNXAySmh4NnZhaTN6Nm5MdVVlZFRLNXhSMGhza0E4V05JWUxYK1pYQS9NYnlqUTBBL1J4NStJY3JDRllTeWFhRW1qYzYiLCJtYWMiOiJkYWRiODU0NDk5YWIwZmMyZjJlYmMyZjUyMTczY2E0YmIzMGY5MzJhMjU2MmM4MTA3ODE2ZWFhNTRhMWE5YzFhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InpjY2poZ2daR28vRlBsQkE5NWRmQlE9PSIsInZhbHVlIjoicU05Qkw4VnBRQ1RsOGhhYzFEM2k4TWh4SGk1MDl5YzYwZHgrd0dEa2plaEcrZE1XTDBMZU1hQlppUG5CMnNpc3Q3ZFIwRFhBYVFEeHFVTEZ6alBKd21WWmMwR243K21ZR1NzeHAvckNBb2RCQ1p6WjFrQ2xnRnh1SWxKaVJwZldtNno3WkxpSFJVekQ4QW1yMlNsSFJUMUV6NEhWdWhZa0FJZDVqdXlsZU1rRHRjZEpDam5INldOdlRtREV2aEZPRXlqd29rVEdUb3Vxd3FkVkptWWlHWHZ4ZEUvQm5vMFVWdDVBK3RVZG1LbXJVSUtTMlB3QnNFK2NuTDVDcUJ2UkprWmlNSGpvK3VhNzQzVXE1QkdYUjdkc1VUenVpcHduOUhYeXdUQ3QxaXRTTldJWHFNUnpPK2JOS28rbFFNOTdqTDBCTjZKQlFaSzY3T2dOM3VtUGJpZTI4aFNpanJzWUZ1TFFaaTRlOHBmNk8rQUs1VWNaemdTT0NhcDhENTY0cWxaYjI4QzNpVUNDUW1NS3RFTEFRRCtxTHFnTGRRRVZlRmJGb1k0Y1BXMXVMdjhwVWM5YWNpZEVlT3JEdmsvTHpHZXRsUm5pNkNITnc1YlZiSkZrQmNNeEFEb1ZtN1RCVWIxMW5hZWMvTmlXcE14a2xpc1pQdjB2b0paZUJCanIiLCJtYWMiOiI4YThlNzVlMGJlMDg1ODg2OWJhZDYzMDkzOWM5YzdlZjE2ZGE0YjcxMjJmMzUyZTk3YzEwMmM1ZmU3MTZmN2JlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1452993758 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xy8o1OLZEDaYyV6WX9XzzYzbw5zHFfm46SvuTNQ8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452993758\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNUZmlHR0ZCWm8zSGIrOWh5dkRpbXc9PSIsInZhbHVlIjoibzZFM0x5RStCOVd3OEZmUkVRQzVKSm03MGhyajBwQktjNW9wcUg4VHVSMjhOVFJBeXN3UUNlRmhDNHN3ZFRPSmZicXpzcTFTS0RCYlNqcmNYVGFkNEE5VjZxeEdURjJQelRlSUk3akhlVnU0MmhoVTNlc2IrdzJydXByaUtYRFZoMGxZSWVVSVhnc3N4cmhnanA1WjhPM0dWdUxvSFcvWGZHdEJ5K1hvMTZiY2l0eXNKWTJHU3BZZXREc1Z4N3U0b2FmSzhyQTBVSUpvRjErbHdqZTYrdTFLVCt2M0UveVVnbWY1dkNSZkE2Nm9VVG8yQ3A0YUZiamJUZU5BMG11NnBML0dBZ3lDSHNjeFd2TSs3aGg0SkllejhuZ3EwNVdyWWZsRjlWeDJqWC9SY2dpc2pvMVFuU2x0VDd3QVc2SzZvTXY2cTZtRURnd21IN0o1aVdOVVhONksweTRZdDdpRTNXVzJzWXpRUHEwNXFTdDNvRVlzVkFOK2FOcGVHK2F5VkRpaWpLc0RlVm5nanE3dXdZYVZKbzNrMHVqdXJVaFl6ZW8wdmQ1Mm05YWhyMk9yWkZnazVIMjRtNlNWcEZQVjlhcWl2T3JObTdVTHhtNEdQM0FGMlJraWNkaW4vZG5WSkNNZm9HSk9IMFNnRFhTVmlwcm9Ub0VLZ1FLblFxU1EiLCJtYWMiOiI5MmQ3NzgzNzcxM2NlMmFhMjU3ZjkyNGVlOTUwYjQzYjI3ZjdmYzE4NmEwYjNlZWFiNjZiYWE0MWNiMGFkMmUzIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImwzeE5yOWd3Wk1lMU84azB0dnZGcGc9PSIsInZhbHVlIjoiVVdjRXdzMDZWeml6dmRNOUlEaU5iVzM2cDN0WDV6RHlaaWlJTnRPVGRLYjh0NjJOSjN0NnZoQTZ0NXp2MGM0NFY3bHc1YjF6dTgzOTlZdVBTU2FRYWppemgxVkxLQ1JNcjV3cFI4ZkYvK2hLR3E2TEF3Q2kvVWtqK0Faa1I0T2sxSTFQeGg5ejdCVCtkOWRwUXF0TzlWTXA0bkczRVl6ODdtUWgvVXVPc2I0TUpXVWNIWlIwMlAwWnBDZHRNZ2VMTHRUd1BReE5VRnhIeXJWZ0tONi9JK2VFRHJaOHcya1labUZmNk0zS2pIODJxd3Zad3IxT0FJVDVyTThaMG9MakY5clhyeU5Bejk1SjZrYnlvek1VazlIa25kcWl5QVp1YmY2ejVIOG52TFNOSDlROS9Kb3NOcjhCeS9mUTREUnE5eWlPVjJZT3FpTDRxNDl3WVJsblE0eXlod0JLRVUybEtrbEdYZlZsMkJ5SXNPTXNHb2dBdDI5a21WOHpjVHNzdHMxOUxVWlJ3cVJ0QXpXZ2JvOW5aRzhmRFhPTHBPSlZTWlVaMndETUN2elNuR3R0THpTd2FLMGZWRU9LZm9Ydk5CL0o2UmJDbG4xcERyT21SRU1hWXJTMEwrV3YxQUNldVlQdHl1U1RuZ29HalZ0bGxhYkF2V0t2OVFrYlNSN2wiLCJtYWMiOiJmMjE2MjhhMGJlODMyMjMxMjFkODIwZTZlN2VjZWNiZGY0ODgyMGViMjYzZWZkYzMxOTI1Nzk1NjY2MDdkNWQ3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNUZmlHR0ZCWm8zSGIrOWh5dkRpbXc9PSIsInZhbHVlIjoibzZFM0x5RStCOVd3OEZmUkVRQzVKSm03MGhyajBwQktjNW9wcUg4VHVSMjhOVFJBeXN3UUNlRmhDNHN3ZFRPSmZicXpzcTFTS0RCYlNqcmNYVGFkNEE5VjZxeEdURjJQelRlSUk3akhlVnU0MmhoVTNlc2IrdzJydXByaUtYRFZoMGxZSWVVSVhnc3N4cmhnanA1WjhPM0dWdUxvSFcvWGZHdEJ5K1hvMTZiY2l0eXNKWTJHU3BZZXREc1Z4N3U0b2FmSzhyQTBVSUpvRjErbHdqZTYrdTFLVCt2M0UveVVnbWY1dkNSZkE2Nm9VVG8yQ3A0YUZiamJUZU5BMG11NnBML0dBZ3lDSHNjeFd2TSs3aGg0SkllejhuZ3EwNVdyWWZsRjlWeDJqWC9SY2dpc2pvMVFuU2x0VDd3QVc2SzZvTXY2cTZtRURnd21IN0o1aVdOVVhONksweTRZdDdpRTNXVzJzWXpRUHEwNXFTdDNvRVlzVkFOK2FOcGVHK2F5VkRpaWpLc0RlVm5nanE3dXdZYVZKbzNrMHVqdXJVaFl6ZW8wdmQ1Mm05YWhyMk9yWkZnazVIMjRtNlNWcEZQVjlhcWl2T3JObTdVTHhtNEdQM0FGMlJraWNkaW4vZG5WSkNNZm9HSk9IMFNnRFhTVmlwcm9Ub0VLZ1FLblFxU1EiLCJtYWMiOiI5MmQ3NzgzNzcxM2NlMmFhMjU3ZjkyNGVlOTUwYjQzYjI3ZjdmYzE4NmEwYjNlZWFiNjZiYWE0MWNiMGFkMmUzIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImwzeE5yOWd3Wk1lMU84azB0dnZGcGc9PSIsInZhbHVlIjoiVVdjRXdzMDZWeml6dmRNOUlEaU5iVzM2cDN0WDV6RHlaaWlJTnRPVGRLYjh0NjJOSjN0NnZoQTZ0NXp2MGM0NFY3bHc1YjF6dTgzOTlZdVBTU2FRYWppemgxVkxLQ1JNcjV3cFI4ZkYvK2hLR3E2TEF3Q2kvVWtqK0Faa1I0T2sxSTFQeGg5ejdCVCtkOWRwUXF0TzlWTXA0bkczRVl6ODdtUWgvVXVPc2I0TUpXVWNIWlIwMlAwWnBDZHRNZ2VMTHRUd1BReE5VRnhIeXJWZ0tONi9JK2VFRHJaOHcya1labUZmNk0zS2pIODJxd3Zad3IxT0FJVDVyTThaMG9MakY5clhyeU5Bejk1SjZrYnlvek1VazlIa25kcWl5QVp1YmY2ejVIOG52TFNOSDlROS9Kb3NOcjhCeS9mUTREUnE5eWlPVjJZT3FpTDRxNDl3WVJsblE0eXlod0JLRVUybEtrbEdYZlZsMkJ5SXNPTXNHb2dBdDI5a21WOHpjVHNzdHMxOUxVWlJ3cVJ0QXpXZ2JvOW5aRzhmRFhPTHBPSlZTWlVaMndETUN2elNuR3R0THpTd2FLMGZWRU9LZm9Ydk5CL0o2UmJDbG4xcERyT21SRU1hWXJTMEwrV3YxQUNldVlQdHl1U1RuZ29HalZ0bGxhYkF2V0t2OVFrYlNSN2wiLCJtYWMiOiJmMjE2MjhhMGJlODMyMjMxMjFkODIwZTZlN2VjZWNiZGY0ODgyMGViMjYzZWZkYzMxOTI1Nzk1NjY2MDdkNWQ3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1270690452 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270690452\", {\"maxDepth\":0})</script>\n"}}