{"__meta": {"id": "Xba893f2b7188dd32ea61e19e3e37fc4b", "datetime": "2025-06-16 09:53:37", "utime": **********.482804, "method": "GET", "uri": "/negative-stock-settings", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067615.58732, "end": **********.48284, "duration": 1.8955199718475342, "duration_str": "1.9s", "measures": [{"label": "Booting", "start": 1750067615.58732, "relative_start": 0, "end": **********.12062, "relative_end": **********.12062, "duration": 1.5332999229431152, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120646, "relative_start": 1.5333259105682373, "end": **********.482843, "relative_end": 2.86102294921875e-06, "duration": 0.3621969223022461, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48104840, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET negative-stock-settings", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\NegativeStockSettingsController@index", "namespace": null, "prefix": "", "where": [], "as": "negative.stock.settings", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FNegativeStockSettingsController.php&line=15\" onclick=\"\">app/Http/Controllers/NegativeStockSettingsController.php:15-22</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03613, "accumulated_duration_str": "36.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.296742, "duration": 0.03019, "duration_str": "30.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.559}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.368547, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.559, "width_percent": 5.729}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4339032, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 89.289, "width_percent": 5.619}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.445951, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.907, "width_percent": 5.093}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage system settings,\n  result => null,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1114052203 data-indent-pad=\"  \"><span class=sf-dump-note>manage system settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">manage system settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114052203\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.462774, "xdebug_link": null}]}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/negative-stock-settings\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/negative-stock-settings", "status_code": "<pre class=sf-dump id=sf-dump-806072191 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-806072191\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-724213325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-724213325\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-849057778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-849057778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IlVLazdZMTNrd3AvVXVvTzVnYldyTXc9PSIsInZhbHVlIjoiTWZsZnBRM0M5L1lZMXN5OHFDcXVKdVRrY0tyR2loc0dVVEFRUDhvU3R2T2JWSTFydW1lTEI3SnYxdlkvUnFlMzR1d3dnZG9GTWl2aStmNlpPY2tLZyt4anU4MUlEL3dZYi91WGkwNEFoSUxOU2ZJR1hJNXJFZVkwRWRyazJtN1ROVzM3Q2VmazIxb0JLQjB5QnJhVWx0MlVvV1E1T1RHUkk0aHY2T0x5TmtVam9xVTFuT28yY2hLM2VneTU4bFdIakZtVkE0cXNxVzhXczhlTkdCUnNDTGxqdFJXSE9rMWQzYnBJczdlSHpwcUxaSlVLM1Q1VzM5QVB0SXZlNE9ZcmRpZisxSHQ3NVgyZXd5SkY2Z1dZTDQ1TW5JbkdmUG4xczBzeEdLNlJrblZUMmVod3lnUUVKak9Jb0FsdzdDV04zOHZoTVNCM2RncmNGVWlVSkxHUjF4SXZTcmJRU05zY1dvQVYwYmQ1V3V1V3RjZytrdkF6M0o0N0lYMU1rdkxycG1xb2VpOGo2SERKeko0SGxtMkRQWWR2MkdXUitEK1dVcUsxRS9oMlEwWDkrQVVoVnF0alBQV04xZW8rTmY5ZGJJRDQ5ZG9XQTBrL1FBN2t5YWlnSm1pUTN0WW1tRlNqT0tqVUFlbzZnYk93cnZaMlFjUzhhS0JnUmRJZDdlZGciLCJtYWMiOiI5YzUzNGMwNTk5MjlmMzllODc1OTNhOGFmODI4Y2VlMDRkMGZiN2U3NjA4MjJkYjNiOWI0NjEwNjAwNmZlOTBkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InRjM09pN0ZXcm56T2FWZnhoTC9HUXc9PSIsInZhbHVlIjoicTU1VEJtSzZsS1lBdXl0ZTZsWGV3NXhSZ0xwWHBBNnExN0pNZWlSbGNtcVV2SzQ3MXBDNjk4aVZPUkU4alBlUFJsU2JOQUdFb3I0aGpXTEJ6ZHV3M3YyWjlZUmZXeW51QUNsL0xoemRhRUl1TzF3Nml0UzQybnUxbFZES3YvY3BzYTArZzZYWGJDNlBvTWkzaDJOTGxCNG1KcXh1QjNmajRVUGJPYUJzM0Z0VXFjc2xQK2tGY25raW92Y1FhWEtrV0t6SlNldUVzYnJQdm96REJ3eWQ3eHpweExFV05pbkhyb3hackZmaXpPZWRuZElaRWxkaGZkckV1R1NnbGx0bDN5UzlFWjR5RDJ0RXdqbFp6K2NvZ2ErL3R2bXlvRGtmeCtxWG16NWRRUzREaXBCeTFLWS93RWVad0p0N1dUaFhyYlA3RmJUK0J4Tjd3WXFKSlF1NUhJcGx6TzZmQVBFU3NhMWdnS1lzVFplYkxTeU5yTjFDemtNZWZkc2UxMk1nUUhnWUdzTkFMZDZsVm5iam91VWV2dlV4WEttY2hYSlFWQUtXZnJ0NUFNc3haRXp4Wk1QcXdkei94WWI0YmF5TjdhOHdvWjEvUE5XSFRtaXBwSk1PRjJrK3ZkQjRUZmpvR0JjY3BaaDdQbjBZT3RrWGlFUDhvdndMVktxeWhTMlkiLCJtYWMiOiIxYTY3OWUzMWQ4MTU2OGJhZTcxMzQ3ZDZlMWY0M2FjYTU5OTUwYWEwODRmYzIyNWQ1ZmZmMzBmM2Y5MjA1ODFmIiwidGFnIjoiIn0%3D; _clsk=116a2vl%7C1750067612209%7C4%7C1%7Cq.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HwbyfD6Wll9HTcS06kaTWGT8T0WOfpZZcPWsOclz</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:53:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRqdy9lRUdCTUVPdXgyUHZYanRmSlE9PSIsInZhbHVlIjoiaVk1Q0x1QWZXMlZPMFZOWElYMWxHb1NWSlhzN3Z1TW5hS0pieE5GK3FTVlpSak5NOVhpS3ZCajI2d0FFN3NHVHJMMmxFUWRPMkxDWmdEd3ZjdEJRakdTWnovaXgwQjE4Qy9xMlBQWEMyZXJodFRHSHVLb3hRV1hkNXFkVDhlTVl1cngyYkl5aEExTURVbzhOdkR2REhrbTZ2eEEra2lMSHB2eGJzNXhSeHBONkpIWG1LQzNNdjJ0Q0Jtb1N4UCtoNUR5OVd4L3lZTUZWTXpkNFVNMy9NZDZrODdHYnBJVThva09NQmkvSnlDSE9ON01nMnZGazkvZ0FpNHZOMndiclZEL0YxZDdBUE5zejJ5SnI0bEF6YjZrUC9SMENibmtoQmdwZTlVT2ZackRZMmQwTk9iUVdJN2FlbWUyTS91cG9rZFphMjNsVW1uYjZmMVByN1dBSGNFVDZ2VGRBRi9YVktONU9POFNjSFhUSDk3QUFhWndWZzVkakFBalJZbjFveFgzbzFVemQrZ0NzOVVsODNUTURMT1BHaEZFTmRmR2FSSmpZVjJFbEdjRVBWZFVYYXdnL3ltOUgyTE5ZNGNoRzhVTGdCSGY0RGkyREtyNnE2a1EyckZPWkRiNW81TDk2YmdOaytRdHpYUWFTVVErQzRGSnhxV1duaUdjNlVobHkiLCJtYWMiOiIwYzI0NGI2MjExY2NhZTYzOTFkNjQ2YTY2ZmJkZTE3OWIyNjM1MWZiZDAzNzdlZTY5OTU1Njc4OTQzOTg0YjhjIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InhtU3R4Y2p2cjY4TmZHeTJHUFdOQnc9PSIsInZhbHVlIjoiMFdCREhBNnJmRXM1VHV4d1FZM1FTT3AvVDRqNDJYOEg2RG1ITWJHa0d6M0h3RHlDZzRqWmcya0Y3VzMydTIycTZVdFcrQzdjZUNrTzB1UURNSmp2ZWJaSHFHbkYrUUhUZ1AxVE5OWDBNc1NrR1U1Q0luMWdONThXa0xRbkpFNElxMDJSREVXTXZaTlV3K0IzTUJ5YUhQOVEwMkdacmN4S01RL01RUkJWUTlpSytKOVovRkluSnF0UFVxYmxtUXpzY2h6SzJ4TkwveXowaUcrdWpHZXJLYnAwczdFVFZtOFRnODFiN2hQN1VvdGV1bisyNXFONnNkSFlZbG5Wd0V3aWswTk4wdmZaZUMrR2pXY2NjK3JlWkJ5WW1CYXAwMWNpY3dkaVl2MnB0RmtGM3R4WG1EbHcySU5LYTdjZFpRdzJrUDVLdWRmUXdpMFYwUGJpeld3bHE1ZTcxQlJkaDhJN01tT2Z0eDR2ZHd5cW9ielZLNkIwK1pGa05FZFl4RGZwUENmaFhnTHFmZnlnN3ZmWVVqSU8wTktGRlhKbjlLWkRadGNlb3RYUzFueUdGYk9sSXdKQm5aWkZUVmZZMVYwaWhWdmdiS3BkTmE2MVBXWnhwc2VKS3pNQ3I3ZUcvQmYvMEVRYmV4TWpZZ2JnaVpwa1BvQXZPRWZJYzVEbElVMU8iLCJtYWMiOiI3MjBmZGQxYWRmYzNjNjhkNjZkOGMxNDg1MjJlNTVmODBiNDM1NzI3YWE5M2E4Njk3OTlmYTAxNDc5OTRkYjI4IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:53:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRqdy9lRUdCTUVPdXgyUHZYanRmSlE9PSIsInZhbHVlIjoiaVk1Q0x1QWZXMlZPMFZOWElYMWxHb1NWSlhzN3Z1TW5hS0pieE5GK3FTVlpSak5NOVhpS3ZCajI2d0FFN3NHVHJMMmxFUWRPMkxDWmdEd3ZjdEJRakdTWnovaXgwQjE4Qy9xMlBQWEMyZXJodFRHSHVLb3hRV1hkNXFkVDhlTVl1cngyYkl5aEExTURVbzhOdkR2REhrbTZ2eEEra2lMSHB2eGJzNXhSeHBONkpIWG1LQzNNdjJ0Q0Jtb1N4UCtoNUR5OVd4L3lZTUZWTXpkNFVNMy9NZDZrODdHYnBJVThva09NQmkvSnlDSE9ON01nMnZGazkvZ0FpNHZOMndiclZEL0YxZDdBUE5zejJ5SnI0bEF6YjZrUC9SMENibmtoQmdwZTlVT2ZackRZMmQwTk9iUVdJN2FlbWUyTS91cG9rZFphMjNsVW1uYjZmMVByN1dBSGNFVDZ2VGRBRi9YVktONU9POFNjSFhUSDk3QUFhWndWZzVkakFBalJZbjFveFgzbzFVemQrZ0NzOVVsODNUTURMT1BHaEZFTmRmR2FSSmpZVjJFbEdjRVBWZFVYYXdnL3ltOUgyTE5ZNGNoRzhVTGdCSGY0RGkyREtyNnE2a1EyckZPWkRiNW81TDk2YmdOaytRdHpYUWFTVVErQzRGSnhxV1duaUdjNlVobHkiLCJtYWMiOiIwYzI0NGI2MjExY2NhZTYzOTFkNjQ2YTY2ZmJkZTE3OWIyNjM1MWZiZDAzNzdlZTY5OTU1Njc4OTQzOTg0YjhjIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InhtU3R4Y2p2cjY4TmZHeTJHUFdOQnc9PSIsInZhbHVlIjoiMFdCREhBNnJmRXM1VHV4d1FZM1FTT3AvVDRqNDJYOEg2RG1ITWJHa0d6M0h3RHlDZzRqWmcya0Y3VzMydTIycTZVdFcrQzdjZUNrTzB1UURNSmp2ZWJaSHFHbkYrUUhUZ1AxVE5OWDBNc1NrR1U1Q0luMWdONThXa0xRbkpFNElxMDJSREVXTXZaTlV3K0IzTUJ5YUhQOVEwMkdacmN4S01RL01RUkJWUTlpSytKOVovRkluSnF0UFVxYmxtUXpzY2h6SzJ4TkwveXowaUcrdWpHZXJLYnAwczdFVFZtOFRnODFiN2hQN1VvdGV1bisyNXFONnNkSFlZbG5Wd0V3aWswTk4wdmZaZUMrR2pXY2NjK3JlWkJ5WW1CYXAwMWNpY3dkaVl2MnB0RmtGM3R4WG1EbHcySU5LYTdjZFpRdzJrUDVLdWRmUXdpMFYwUGJpeld3bHE1ZTcxQlJkaDhJN01tT2Z0eDR2ZHd5cW9ielZLNkIwK1pGa05FZFl4RGZwUENmaFhnTHFmZnlnN3ZmWVVqSU8wTktGRlhKbjlLWkRadGNlb3RYUzFueUdGYk9sSXdKQm5aWkZUVmZZMVYwaWhWdmdiS3BkTmE2MVBXWnhwc2VKS3pNQ3I3ZUcvQmYvMEVRYmV4TWpZZ2JnaVpwa1BvQXZPRWZJYzVEbElVMU8iLCJtYWMiOiI3MjBmZGQxYWRmYzNjNjhkNjZkOGMxNDg1MjJlNTVmODBiNDM1NzI3YWE5M2E4Njk3OTlmYTAxNDc5OTRkYjI4IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:53:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-5386296 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost/negative-stock-settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5386296\", {\"maxDepth\":0})</script>\n"}}