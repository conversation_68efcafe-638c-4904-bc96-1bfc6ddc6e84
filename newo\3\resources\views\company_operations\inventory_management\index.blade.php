@extends('layouts.admin')

@section('page-title')
    {{ __('إدارة المخزون') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('لوحة التحكم') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة المخزون') }}</li>
@endsection

@push('css-page')
    <style>
        .low-stock {
            background-color: rgba(255, 91, 91, 0.1) !important;
        }
        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }
        .editable-cell:hover {
            background-color: rgba(111, 217, 67, 0.1);
        }
        .editable-cell:hover::after {
            content: "\f044";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            right: 5px;
            color: #6fd943;
            animation: pulse 1.5s infinite;
        }
        .edit-input {
            width: 100%;
            padding: 8px;
            border: 2px solid #6fd943;
            border-radius: 4px;
            box-shadow: 0 0 10px rgba(111, 217, 67, 0.2);
            transition: all 0.3s ease;
        }
        .edit-input:focus {
            box-shadow: 0 0 15px rgba(111, 217, 67, 0.4);
        }
        .stats-card {
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .stats-card .icon-box {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .stats-card .bg-primary-light {
            background-color: rgba(80, 143, 244, 0.1);
            color: #508ff4;
        }
        .stats-card .bg-success-light {
            background-color: rgba(111, 217, 67, 0.1);
            color: #6fd943;
        }
        .stats-card .bg-danger-light {
            background-color: rgba(255, 91, 91, 0.1);
            color: #ff5b5b;
        }
        .stats-card .bg-warning-light {
            background-color: rgba(255, 171, 0, 0.1);
            color: #ffab00;
        }
        .stats-card .bg-info-light {
            background-color: rgba(23, 162, 184, 0.1);
            color: #17a2b8;
        }
        .filter-section {
            background-color: #f8f9fd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .custom-select {
            background-color: white;
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }
        .custom-select:focus {
            border-color: #6fd943;
            box-shadow: 0 0 0 0.2rem rgba(111, 217, 67, 0.25);
        }
        .custom-input {
            background-color: white;
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }
        .custom-input:focus {
            border-color: #6fd943;
            box-shadow: 0 0 0 0.2rem rgba(111, 217, 67, 0.25);
        }
        .btn-custom-primary {
            background-color: #6fd943;
            border-color: #6fd943;
            color: white;
            border-radius: 6px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        .btn-custom-primary:hover {
            background-color: #5dc52e;
            border-color: #5dc52e;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(111, 217, 67, 0.3);
        }
        .btn-custom-success {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
            border-radius: 6px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        .btn-custom-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        .btn-custom-secondary {
            background-color: #f8f9fd;
            border-color: #e0e6ed;
            color: #506690;
            border-radius: 6px;
            padding: 10px 20px;
            transition: all 0.3s ease;
        }
        .btn-custom-secondary:hover {
            background-color: #e0e6ed;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .page-header {
            background: linear-gradient(135deg, #6fd943 0%, #508ff4 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.6;
            }
            100% {
                opacity: 1;
            }
        }
        .datatable {
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        .datatable thead th {
            background-color: #f8f9fd;
            color: #506690;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
            padding: 15px;
            border-bottom: 2px solid #e0e6ed;
        }
        .datatable tbody tr {
            transition: all 0.3s ease;
        }
        .datatable tbody tr:hover {
            background-color: rgba(111, 217, 67, 0.05);
        }
        .datatable tbody td {
            padding: 15px;
            border-bottom: 1px solid #e0e6ed;
            vertical-align: middle;
        }
        .badge-custom-success {
            background-color: rgba(111, 217, 67, 0.1);
            color: #6fd943;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 6px;
        }
        .badge-custom-danger {
            background-color: rgba(255, 91, 91, 0.1);
            color: #ff5b5b;
            font-weight: 500;
            padding: 5px 10px;
            border-radius: 6px;
        }
    </style>
@endpush

@section('content')
    <!-- Page Header -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-auto">
                <div class="page-header-icon">
                    <i class="fas fa-boxes fa-2x"></i>
                </div>
            </div>
            <div class="col">
                <h2 class="page-title">{{ __('إدارة المخزون') }}</h2>
                <div class="text-white-50">
                    {{ __('إدارة مخزون المنتجات ومراقبة الكميات وتحديث البيانات') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row" id="stats-container">
        <div class="col-xl-2 col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-primary-light me-3">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ __('إجمالي المنتجات') }}</h6>
                            <h3 class="mb-0 total-products">-</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-success-light me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ __('منتجات بكميات كافية') }}</h6>
                            <h3 class="mb-0 normal-stock">-</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-danger-light me-3">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ __('منتجات بكميات منخفضة') }}</h6>
                            <h3 class="mb-0 low-stock-count">-</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-warning-light me-3">
                            <i class="fas fa-box-open"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ __('منتجات غير متوفرة') }}</h6>
                            <h3 class="mb-0 out-of-stock-count">-</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="icon-box bg-info-light me-3">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ __('المستودع الحالي') }}</h6>
                            <h3 class="mb-0 current-warehouse">-</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <button class="btn btn-custom-primary btn-sm w-100" onclick="addAllProductsToWarehouse()">
                        <i class="fas fa-plus-circle me-1"></i>
                        {{ __('إضافة جميع المنتجات') }}
                    </button>
                    <small class="text-muted d-block mt-1">{{ __('إضافة المنتجات غير الموجودة') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>{{ __('تصفية وبحث') }}</h5>
                <div class="card-actions">
                    <button class="btn btn-sm btn-custom-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                        <i class="fas fa-chevron-down me-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="collapse show" id="filterCollapse">
            <div class="card-body filter-section">
                <form id="warehouse-filter-form" class="row align-items-end">
                    <div class="col-md-3 form-group mb-3">
                        <label for="warehouse_id" class="form-label fw-bold">
                            <i class="fas fa-warehouse me-1"></i> {{ __('المستودع') }}
                        </label>
                        <select name="warehouse_id" id="warehouse_id" class="form-control custom-select">
                            @foreach($warehouses as $warehouse)
                                <option value="{{ $warehouse->id }}" {{ $selectedWarehouse == $warehouse->id ? 'selected' : '' }}>
                                    {{ $warehouse->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3 form-group mb-3">
                        <label for="search" class="form-label fw-bold">
                            <i class="fas fa-search me-1"></i> {{ __('بحث') }}
                        </label>
                        <input type="text" id="search" name="search" class="form-control custom-input" placeholder="{{ __('بحث عن منتج...') }}">
                    </div>
                    <div class="col-md-3 form-group mb-3">
                        <label for="status_filter" class="form-label fw-bold">
                            <i class="fas fa-chart-line me-1"></i> {{ __('حالة المخزون') }}
                        </label>
                        <select name="status_filter" id="status_filter" class="form-control custom-select">
                            <option value="">{{ __('جميع الحالات') }}</option>
                            <option value="out_of_stock">{{ __('غير متوفر') }}</option>
                            <option value="low_stock">{{ __('منخفض') }}</option>
                            <option value="normal">{{ __('طبيعي') }}</option>
                        </select>
                    </div>
                    <div class="col-md-3 form-group mb-3">
                        <div class="d-flex">
                            <button type="submit" class="btn btn-custom-primary me-2">
                                <i class="fas fa-search me-1"></i> {{ __('بحث') }}
                            </button>
                            <button type="button" id="reset-filter" class="btn btn-custom-secondary">
                                <i class="fas fa-redo me-1"></i> {{ __('إعادة تعيين') }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>{{ __('قائمة المنتجات') }}</h5>
                <div class="card-actions">
                    <button class="btn btn-sm btn-custom-info me-2" id="add-quantity-btn" onclick="addQuantityToAll()" style="display: none;" title="{{ __('إضافة +1 للمنتجات التي كميتها صفر') }}">
                        <i class="fas fa-plus me-1"></i> {{ __('إضافة كمية') }}
                    </button>
                    <button class="btn btn-sm btn-custom-success me-2" id="print-inventory-btn" onclick="printInventoryReport()" style="display: none;">
                        <i class="fas fa-print me-1"></i> {{ __('طباعة الجرد') }}
                    </button>
                    <button class="btn btn-sm btn-custom-primary" id="refresh-products">
                        <i class="fas fa-sync-alt me-1"></i> {{ __('تحديث') }}
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="products-container">
                <!-- سيتم تحميل جدول المنتجات هنا -->
                <div class="text-center py-5">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">{{ __('جاري تحميل البيانات...') }}</p>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // تحميل المنتجات عند تحميل الصفحة
        loadWarehouseProducts();

        // تهيئة Modal الصورة
        initImageModal();

        // تحميل المنتجات عند تغيير المستودع
        $('#warehouse_id').change(function() {
            // إخفاء الأزرار عند تغيير المستودع
            $('#print-inventory-btn').hide();
            $('#add-quantity-btn').hide();
            loadWarehouseProducts();
        });

        // تحميل المنتجات عند تقديم نموذج البحث
        $('#warehouse-filter-form').submit(function(e) {
            e.preventDefault();
            loadWarehouseProducts();
        });

        // إعادة تعيين التصفية
        $('#reset-filter').click(function() {
            $('#search').val('');
            $('#status_filter').val('');
            loadWarehouseProducts();
        });

        // تحديث المنتجات
        $('#refresh-products').click(function() {
            // إضافة تأثير دوران للأيقونة
            $(this).find('i').addClass('fa-spin');

            // تحميل المنتجات
            loadWarehouseProducts();

            // إزالة تأثير الدوران بعد ثانية
            setTimeout(function() {
                $('#refresh-products').find('i').removeClass('fa-spin');
            }, 1000);

            // إظهار رسالة نجاح
            showNotification('success', '{{ __("تم تحديث البيانات بنجاح") }}');
        });

        // وظيفة تحميل منتجات المستودع
        function loadWarehouseProducts() {
            var warehouseId = $('#warehouse_id').val();
            var search = $('#search').val();
            var statusFilter = $('#status_filter').val();

            $('#products-container').html('<div class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">{{ __("جاري تحميل البيانات...") }}</p></div>');

            $.ajax({
                url: "{{ url('inventory-management/products') }}/" + warehouseId,
                type: "GET",
                data: {
                    search: search,
                    status_filter: statusFilter
                },
                success: function(response) {
                    $('#products-container').html(response);
                    initEditableFields();
                    initImageModal();

                    // إظهار الأزرار عند تحميل المنتجات بنجاح
                    $('#print-inventory-btn').show();
                    $('#add-quantity-btn').show();

                    // إظهار رسالة نجاح مع إمكانية الطباعة
                    showNotification('success', '{{ __("تم تحميل البيانات بنجاح. يمكنك الآن طباعة تقرير الجرد.") }}');
                },
                error: function(xhr) {
                    $('#products-container').html('<div class="alert alert-danger">{{ __("حدث خطأ أثناء تحميل البيانات") }}</div>');
                    // إخفاء الأزرار عند حدوث خطأ
                    $('#print-inventory-btn').hide();
                    $('#add-quantity-btn').hide();
                    showNotification('error', '{{ __("حدث خطأ أثناء تحميل البيانات") }}');
                }
            });
        }

        // تهيئة الحقول القابلة للتحرير
        function initEditableFields() {
            // تحرير كمية المنتج
            $('.editable-quantity').click(function() {
                var cell = $(this);
                // استخراج الكمية الحالية من النص (إزالة النصوص الإضافية)
                var currentValue = parseFloat(cell.find('span').first().text()) || 0;
                var warehouseProductId = cell.data('id');

                // التحقق من صحة warehouse_product_id
                if (warehouseProductId === '' || warehouseProductId === 'null' || warehouseProductId === null) {
                    warehouseProductId = null;
                }

                cell.html('<input type="number" class="edit-input" value="' + currentValue + '" min="0">');
                var input = cell.find('input');
                input.focus();

                input.blur(function() {
                    var newValue = parseFloat($(this).val()) || 0;
                    if (newValue !== currentValue) {
                        updateQuantity(warehouseProductId, newValue, cell);
                    } else {
                        // إعادة العرض الأصلي
                        if (currentValue > 0) {
                            cell.html('<span class="fw-bold text-success">' + currentValue + '</span>');
                        } else {
                            cell.html('<span class="fw-bold text-muted">' + currentValue + '</span><small class="d-block text-info"><i class="fas fa-plus-circle me-1"></i>{{ __("انقر لإضافة كمية") }}</small>');
                        }
                    }
                });

                input.keypress(function(e) {
                    if (e.which === 13) {
                        $(this).blur();
                    }
                });
            });

            // تحرير الحد الأدنى للكمية
            $('.editable-min-quantity').click(function() {
                var cell = $(this);
                var currentValue = cell.text().trim() || '0';
                var productId = cell.data('product-id');
                var warehouseId = cell.data('warehouse-id');

                cell.html('<input type="number" class="edit-input" value="' + currentValue + '" min="0">');
                var input = cell.find('input');
                input.focus();

                input.blur(function() {
                    var newValue = $(this).val();
                    if (newValue !== currentValue) {
                        updateMinQuantity(productId, warehouseId, newValue, cell);
                    } else {
                        cell.html('<span class="fw-bold">' + currentValue + '</span>');
                    }
                });

                input.keypress(function(e) {
                    if (e.which === 13) {
                        $(this).blur();
                    }
                });
            });

            // تهيئة تحرير بيانات المنتج
            initProductDataEditing();
        }

        // حساب الحد الأدنى للكمية تلقائياً
        function calculateMinQuantity(currentQuantity) {
            var quantity = parseFloat(currentQuantity) || 0;

            // إذا كانت الكمية أقل من 10 (من 1 إلى 9)، استخدم نفس الكمية
            if (quantity > 0 && quantity < 10) {
                return Math.floor(quantity);
            }

            // إذا كانت الكمية 10 أو أكثر، احسب 30% بدون كسور
            if (quantity >= 10) {
                return Math.floor(quantity * 0.3);
            }

            // إذا كانت الكمية 0، أرجع 0
            return 0;
        }

        // تحديث كمية المنتج أو إضافة منتج جديد
        function updateQuantity(warehouseProductId, quantity, cell) {
            // إظهار مؤشر التحميل
            cell.html('<i class="fas fa-spinner fa-spin"></i>');

            var data = {
                _token: "{{ csrf_token() }}",
                quantity: quantity
            };

            // إذا كان المنتج موجود في المستودع (له warehouse_product_id)
            if (warehouseProductId && warehouseProductId !== '' && warehouseProductId !== 'null') {
                data.warehouse_product_id = warehouseProductId;
            } else {
                // إذا كان منتج جديد أو لا يوجد له سجل في warehouse_products، نحتاج معرف المنتج والمستودع
                data.product_id = cell.data('product-id');
                data.warehouse_id = cell.data('warehouse-id');
            }

            $.ajax({
                url: "{{ route('inventory.management.update.quantity') }}",
                type: "POST",
                data: data,
                success: function(response) {
                    if (response.success) {
                        // تحديث العرض
                        if (parseFloat(quantity) > 0) {
                            cell.html('<span class="fw-bold text-success">' + quantity + '</span>');
                        } else {
                            cell.html('<span class="fw-bold text-muted">' + quantity + '</span><small class="d-block text-info"><i class="fas fa-plus-circle me-1"></i>{{ __("انقر لإضافة كمية") }}</small>');
                        }

                        // تحديث warehouse_product_id للاستخدام المستقبلي
                        if (response.warehouse_product_id) {
                            cell.attr('data-id', response.warehouse_product_id);
                        }

                        // حساب وتحديث الحد الأدنى للكمية تلقائياً
                        var row = cell.closest('tr');
                        var minQuantityCell = row.find('.editable-min-quantity');
                        var calculatedMinQuantity = calculateMinQuantity(quantity);

                        // تحديث الحد الأدنى للكمية في قاعدة البيانات
                        if (calculatedMinQuantity >= 0) {
                            updateMinQuantityAuto(
                                cell.data('product-id'),
                                cell.data('warehouse-id'),
                                calculatedMinQuantity,
                                minQuantityCell
                            );
                        }

                        // تحديث حالة المنتج (منخفض/طبيعي/غير متوفر)
                        var minQuantity = calculatedMinQuantity;

                        if (parseFloat(quantity) == 0) {
                            row.removeClass('low-stock');
                            row.find('.stock-status').html('<span class="badge badge-custom-warning"><i class="fas fa-box-open me-1"></i> {{ __("غير متوفر") }}</span>');
                        } else if (parseFloat(quantity) < minQuantity) {
                            row.addClass('low-stock');
                            row.find('.stock-status').html('<span class="badge badge-custom-danger"><i class="fas fa-exclamation-triangle me-1"></i> {{ __("منخفض") }}</span>');
                        } else {
                            row.removeClass('low-stock');
                            row.find('.stock-status').html('<span class="badge badge-custom-success"><i class="fas fa-check-circle me-1"></i> {{ __("طبيعي") }}</span>');
                        }

                        // تحديث الإحصائيات
                        updateStats();

                        // إظهار رسالة نجاح
                        showNotification('success', response.message);
                    } else {
                        cell.html('<span class="fw-bold">' + response.old_quantity + '</span>');
                        showNotification('error', response.message);
                    }
                },
                error: function(xhr) {
                    cell.html('<span class="fw-bold">' + quantity + '</span>');
                    showNotification('error', "{{ __('حدث خطأ أثناء تحديث الكمية') }}");
                }
            });
        }

        // تحديث الحد الأدنى للكمية تلقائياً (بدون إظهار رسائل)
        function updateMinQuantityAuto(productId, warehouseId, minQuantity, cell) {
            $.ajax({
                url: "{{ route('inventory.management.update.min.quantity') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    product_id: productId,
                    warehouse_id: warehouseId,
                    min_quantity: minQuantity
                },
                success: function(response) {
                    if (response.success) {
                        // تحديث العرض بصمت
                        cell.html('<span class="fw-bold">' + minQuantity + '</span>');
                    }
                },
                error: function(xhr) {
                    // في حالة الخطأ، لا نظهر رسالة خطأ لأن هذا تحديث تلقائي
                    console.log('خطأ في تحديث الحد الأدنى للكمية تلقائياً');
                }
            });
        }

        // تحديث الحد الأدنى للكمية (يدوياً من المستخدم)
        function updateMinQuantity(productId, warehouseId, minQuantity, cell) {
            // إظهار مؤشر التحميل
            cell.html('<i class="fas fa-spinner fa-spin"></i>');

            $.ajax({
                url: "{{ route('inventory.management.update.min.quantity') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    product_id: productId,
                    warehouse_id: warehouseId,
                    min_quantity: minQuantity
                },
                success: function(response) {
                    if (response.success) {
                        cell.html('<span class="fw-bold">' + minQuantity + '</span>');

                        // تحديث حالة المنتج (منخفض/طبيعي)
                        var row = cell.closest('tr');
                        var quantity = parseFloat(row.find('.editable-quantity').text()) || 0;

                        if (quantity < parseFloat(minQuantity)) {
                            row.addClass('low-stock');
                            row.find('.stock-status').html('<span class="badge badge-custom-danger"><i class="fas fa-exclamation-triangle me-1"></i> {{ __("منخفض") }}</span>');
                        } else {
                            row.removeClass('low-stock');
                            row.find('.stock-status').html('<span class="badge badge-custom-success"><i class="fas fa-check-circle me-1"></i> {{ __("طبيعي") }}</span>');
                        }

                        // تحديث الإحصائيات
                        updateStats();

                        // إظهار رسالة نجاح
                        showNotification('success', response.message);
                    } else {
                        cell.html('<span class="fw-bold">' + minQuantity + '</span>');
                        showNotification('error', response.message);
                    }
                },
                error: function(xhr) {
                    cell.html('<span class="fw-bold">' + minQuantity + '</span>');
                    showNotification('error', "{{ __('حدث خطأ أثناء تحديث الحد الأدنى للكمية') }}");
                }
            });
        }

        // تحديث الإحصائيات
        function updateStats() {
            var totalProducts = $('.datatable tbody tr').length;
            var lowStockCount = $('.datatable tbody tr.low-stock').length;
            var outOfStockCount = $('.datatable tbody tr').filter(function() {
                return $(this).find('.stock-status .badge-custom-warning').length > 0;
            }).length;
            var normalStockCount = totalProducts - lowStockCount - outOfStockCount;

            $('.total-products').text(totalProducts);
            $('.low-stock-count').text(lowStockCount);
            $('.normal-stock').text(normalStockCount);
            $('.out-of-stock-count').text(outOfStockCount);
        }

        // إضافة جميع المنتجات إلى المستودع بكمية 0
        function addAllProductsToWarehouse() {
            var warehouseId = $('#warehouse_id').val();

            if (!warehouseId) {
                showNotification('error', '{{ __("يرجى اختيار مستودع أولاً") }}');
                return;
            }

            if (confirm('{{ __("هل تريد إضافة جميع المنتجات غير الموجودة إلى هذا المستودع بكمية 0؟") }}')) {
                // البحث عن المنتجات التي لا تحتوي على warehouse_product_id
                var productsToAdd = [];
                $('.editable-quantity').each(function() {
                    var cell = $(this);
                    var warehouseProductId = cell.data('id');
                    // التحقق من عدم وجود warehouse_product_id أو كونه فارغ أو null
                    if (!warehouseProductId || warehouseProductId === '' || warehouseProductId === 'null') {
                        productsToAdd.push({
                            product_id: cell.data('product-id'),
                            warehouse_id: warehouseId
                        });
                    }
                });

                if (productsToAdd.length === 0) {
                    showNotification('info', '{{ __("جميع المنتجات موجودة بالفعل في هذا المستودع") }}');
                    return;
                }

                // إضافة المنتجات واحد تلو الآخر
                var addedCount = 0;
                var totalCount = productsToAdd.length;

                productsToAdd.forEach(function(product, index) {
                    setTimeout(function() {
                        $.ajax({
                            url: "{{ route('inventory.management.update.quantity') }}",
                            type: "POST",
                            data: {
                                _token: "{{ csrf_token() }}",
                                product_id: product.product_id,
                                warehouse_id: product.warehouse_id,
                                quantity: 0
                            },
                            success: function(response) {
                                addedCount++;
                                if (addedCount === totalCount) {
                                    showNotification('success', '{{ __("تم إضافة جميع المنتجات بنجاح") }}');
                                    loadWarehouseProducts(); // إعادة تحميل البيانات
                                }
                            },
                            error: function() {
                                addedCount++;
                                if (addedCount === totalCount) {
                                    showNotification('warning', '{{ __("تم إضافة معظم المنتجات مع بعض الأخطاء") }}');
                                    loadWarehouseProducts();
                                }
                            }
                        });
                    }, index * 100); // تأخير 100ms بين كل طلب
                });
            }
        }

        // تهيئة Modal الصورة
        function initImageModal() {
            // إزالة الأحداث السابقة لتجنب التكرار
            $(document).off('click', '.clickable-image');

            // إضافة حدث جديد
            $(document).on('click', '.clickable-image', function() {
                var imageSrc = $(this).data('image-src');
                var productName = $(this).data('product-name');

                $('#modalImage').attr('src', imageSrc);
                $('#modalImage').attr('alt', productName);
                $('#modalProductName').text(productName);

                // فتح Modal
                $('#imageModal').modal('show');
            });
        }

        // إظهار إشعار
        function showNotification(type, message) {
            var icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
            var color = type === 'success' ? '#6fd943' : '#ff5b5b';

            var notification = $('<div class="notification ' + type + '"><i class="' + icon + '"></i> ' + message + '</div>');
            notification.css({
                'position': 'fixed',
                'top': '20px',
                'right': '20px',
                'z-index': '9999',
                'background-color': color,
                'color': 'white',
                'padding': '15px 20px',
                'border-radius': '5px',
                'box-shadow': '0 4px 10px rgba(0, 0, 0, 0.1)',
                'display': 'flex',
                'align-items': 'center',
                'opacity': '0',
                'transform': 'translateY(-20px)',
                'transition': 'all 0.3s ease'
            });

            notification.find('i').css({
                'margin-right': '10px',
                'font-size': '18px'
            });

            $('body').append(notification);

            setTimeout(function() {
                notification.css({
                    'opacity': '1',
                    'transform': 'translateY(0)'
                });
            }, 100);

            setTimeout(function() {
                notification.css({
                    'opacity': '0',
                    'transform': 'translateY(-20px)'
                });

                setTimeout(function() {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // تهيئة تحرير بيانات المنتج
        function initProductDataEditing() {
            // تحرير اسم المنتج
            $(document).off('click', '.editable-product-name');
            $(document).on('click', '.editable-product-name', function() {
                editProductField($(this), 'text');
            });

            // تحرير SKU
            $(document).off('click', '.editable-sku');
            $(document).on('click', '.editable-sku', function() {
                editProductField($(this), 'text');
            });

            // تحرير سعر البيع
            $(document).off('click', '.editable-price');
            $(document).on('click', '.editable-price', function() {
                editProductField($(this), 'number');
            });

            // تحرير الضريبة
            $(document).off('click', '.editable-tax');
            $(document).on('click', '.editable-tax', function() {
                editProductField($(this), 'select', 'tax');
            });

            // تحرير الفئة
            $(document).off('click', '.editable-category');
            $(document).on('click', '.editable-category', function() {
                editProductField($(this), 'select', 'category');
            });

            // تحرير الوحدة
            $(document).off('click', '.editable-unit');
            $(document).on('click', '.editable-unit', function() {
                editProductField($(this), 'select', 'unit');
            });
        }

        // دالة عامة لتحرير حقول المنتج
        function editProductField(cell, inputType, selectType = null) {
            if (cell.find('input, select').length > 0) return;

            var productId = cell.data('product-id');
            var field = cell.data('field');
            var currentValue = '';
            var currentText = '';

            // الحصول على القيمة الحالية
            if (inputType === 'select') {
                currentValue = cell.data('current-' + selectType) || '';
                currentText = cell.find('.editable-text').text().trim();
            } else {
                currentText = cell.find('.editable-text').text().trim();
                if (field === 'sale_price') {
                    // إزالة تنسيق العملة للحصول على الرقم فقط
                    currentValue = currentText.replace(/[^\d.]/g, '');
                } else {
                    currentValue = currentText;
                }
            }

            cell.addClass('editing');

            if (inputType === 'select') {
                createSelectField(cell, selectType, currentValue, productId, field, currentText);
            } else {
                createInputField(cell, inputType, currentValue, productId, field, currentText);
            }
        }

        // إنشاء حقل إدخال
        function createInputField(cell, inputType, currentValue, productId, field, currentText) {
            var input = $('<input type="' + inputType + '" class="form-control form-control-sm" value="' + currentValue + '">');

            if (inputType === 'number') {
                input.attr('min', '0').attr('step', '0.01');
            }

            cell.html(input);
            input.focus().select();

            input.on('blur keypress', function(e) {
                if (e.type === 'keypress' && e.which !== 13) return;

                var newValue = $(this).val();
                if (newValue !== currentValue && newValue.trim() !== '') {
                    updateProductData(productId, field, newValue, cell, currentText);
                } else {
                    cell.removeClass('editing');
                    cell.html('<span class="editable-text">' + currentText + '</span>');
                }
            });
        }

        // إنشاء حقل اختيار
        function createSelectField(cell, selectType, currentValue, productId, field, currentText) {
            var select = $('<select class="form-control form-control-sm"><option value="">جاري التحميل...</option></select>');
            cell.html(select);

            // جلب البيانات حسب النوع
            var url = '';
            switch(selectType) {
                case 'tax':
                    url = "{{ route('inventory.management.get.taxes') }}";
                    break;
                case 'category':
                    url = "{{ route('inventory.management.get.categories') }}";
                    break;
                case 'unit':
                    url = "{{ route('inventory.management.get.units') }}";
                    break;
            }

            $.ajax({
                url: url,
                type: 'GET',
                success: function(data) {
                    select.empty();
                    select.append('<option value="">-- اختر --</option>');

                    if (selectType === 'tax') {
                        // للضرائب، يمكن اختيار متعدد
                        data.forEach(function(item) {
                            var selected = currentValue.split(',').includes(item.id.toString()) ? 'selected' : '';
                            select.append('<option value="' + item.id + '" ' + selected + '>' + item.name + ' (' + item.rate + '%)</option>');
                        });
                        select.attr('multiple', true);
                    } else {
                        data.forEach(function(item) {
                            var selected = currentValue == item.id ? 'selected' : '';
                            select.append('<option value="' + item.id + '" ' + selected + '>' + item.name + '</option>');
                        });
                    }

                    select.focus();
                },
                error: function() {
                    cell.removeClass('editing');
                    cell.html('<span class="editable-text">' + currentText + '</span>');
                    showNotification('error', 'حدث خطأ في تحميل البيانات');
                }
            });

            select.on('blur change', function() {
                var newValue = '';
                if (selectType === 'tax') {
                    newValue = $(this).val() ? $(this).val().join(',') : '';
                } else {
                    newValue = $(this).val();
                }

                if (newValue !== currentValue) {
                    updateProductData(productId, field, newValue, cell, currentText);
                } else {
                    cell.removeClass('editing');
                    cell.html('<span class="editable-text">' + currentText + '</span>');
                }
            });
        }

        // تحديث بيانات المنتج
        function updateProductData(productId, field, value, cell, originalText) {
            cell.html('<i class="fas fa-spinner fa-spin"></i>');

            $.ajax({
                url: "{{ route('inventory.management.update.product.data') }}",
                type: 'POST',
                data: {
                    _token: "{{ csrf_token() }}",
                    product_id: productId,
                    field: field,
                    value: value
                },
                success: function(response) {
                    if (response.success) {
                        // إعادة تحميل الصفحة لتحديث جميع البيانات المرتبطة
                        loadWarehouseProducts();
                        showNotification('success', response.message);
                    } else {
                        cell.removeClass('editing');
                        cell.html('<span class="editable-text">' + originalText + '</span>');
                        showNotification('error', response.message);
                    }
                },
                error: function(xhr) {
                    cell.removeClass('editing');
                    cell.html('<span class="editable-text">' + originalText + '</span>');
                    showNotification('error', 'حدث خطأ أثناء تحديث البيانات');
                }
            });
        }

    });

    // وظيفة طباعة الجرد (خارج نطاق document.ready لتكون متاحة عالمياً)
    function printInventoryReport() {
        console.log('تم النقر على زر طباعة الجرد'); // للتشخيص

        var warehouseId = $('#warehouse_id').val();
        var warehouseName = $('#warehouse_id option:selected').text();
        var search = $('#search').val();
        var statusFilter = $('#status_filter').val();

        console.log('معرف المستودع:', warehouseId); // للتشخيص
        console.log('البحث:', search);
        console.log('فلتر الحالة:', statusFilter);

        if (!warehouseId) {
            alert('يرجى اختيار مستودع أولاً');
            return;
        }

        // التأكد من وجود منتجات
        var productsCount = $('#products-container table tbody tr').length;
        console.log('عدد المنتجات:', productsCount); // للتشخيص

        if (productsCount === 0) {
            alert('لا توجد منتجات في هذا المستودع للطباعة');
            return;
        }

        // إعداد رسالة التأكيد مع معلومات التصفية
        var confirmMessage = 'هل تريد طباعة تقرير الجرد لمستودع "' + warehouseName + '"؟';
        if (search || statusFilter) {
            confirmMessage += '\n\nالتصفية المطبقة:';
            if (search) {
                confirmMessage += '\n- البحث: "' + search + '"';
            }
            if (statusFilter) {
                var statusNames = {
                    'out_of_stock': 'غير متوفر',
                    'low_stock': 'منخفض',
                    'normal': 'طبيعي'
                };
                confirmMessage += '\n- الحالة: ' + (statusNames[statusFilter] || statusFilter);
            }
            confirmMessage += '\n\nسيتم طباعة ' + productsCount + ' منتج فقط.';
        }
        confirmMessage += '\n\nسيتم فتح التقرير في نافذة جديدة.';

        if (!confirm(confirmMessage)) {
            return;
        }

        // إظهار مؤشر التحميل
        $('#print-inventory-btn').html('<i class="fas fa-spinner fa-spin me-1"></i> جاري التحضير...');
        $('#print-inventory-btn').prop('disabled', true);

        try {
            // إعداد معاملات التصفية
            var filterParams = [];
            if (search) {
                filterParams.push('search=' + encodeURIComponent(search));
            }
            if (statusFilter) {
                filterParams.push('status_filter=' + encodeURIComponent(statusFilter));
            }
            var queryString = filterParams.length > 0 ? '&' + filterParams.join('&') : '';

            // اختبار الطريق أولاً
            var testUrl = '/inventory-management/print-report/' + warehouseId + '?test=1' + queryString;
            console.log('اختبار الطريق:', testUrl);

            $.ajax({
                url: testUrl,
                type: 'GET',
                success: function(response) {
                    console.log('نتيجة اختبار الطريق:', response);

                    if (response.success) {
                        // الطريق يعمل، الآن نفتح PDF
                        var printUrl = '/inventory-management/print-report/' + warehouseId + '?' + filterParams.join('&');
                        console.log('رابط الطباعة:', printUrl);

                        var printWindow = window.open(printUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

                        if (!printWindow) {
                            alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
                        } else {
                            console.log('تم فتح نافذة PDF بنجاح');
                            if (typeof showNotification === 'function') {
                                showNotification('success', 'تم فتح تقرير الجرد في نافذة جديدة');
                            }
                        }
                    } else {
                        alert('فشل في اختبار الطريق: ' + (response.message || 'خطأ غير معروف'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في اختبار الطريق:', xhr.responseText);
                    alert('خطأ في الاتصال بالخادم: ' + error + '\nالاستجابة: ' + xhr.responseText);
                }
            });

        } catch (error) {
            console.error('خطأ في الطباعة:', error);
            alert('حدث خطأ أثناء فتح التقرير: ' + error.message);
        }

        // إعادة تعيين الزر
        setTimeout(function() {
            $('#print-inventory-btn').html('<i class="fas fa-print me-1"></i> طباعة الجرد');
            $('#print-inventory-btn').prop('disabled', false);
        }, 2000);
    }

    // دالة إضافة كمية +1 لجميع المنتجات
    function addQuantityToAll() {
        var warehouseId = $('#warehouse_id').val();

        if (!warehouseId) {
            showNotification('error', '{{ __("يرجى اختيار مستودع أولاً") }}');
            return;
        }

        // تأكيد العملية
        var confirmMessage = '{{ __("هل أنت متأكد من إضافة +1 للمنتجات التي كميتها صفر في هذا المستودع؟") }}';
        if (!confirm(confirmMessage)) {
            return;
        }

        // إظهار مؤشر التحميل
        $('#add-quantity-btn').html('<i class="fas fa-spinner fa-spin me-1"></i> {{ __("جاري الإضافة...") }}');
        $('#add-quantity-btn').prop('disabled', true);

        // إرسال طلب AJAX
        $.ajax({
            url: '{{ route("inventory.add.quantity.all") }}',
            method: 'POST',
            data: {
                warehouse_id: warehouseId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    showNotification('success', response.message);
                    // إعادة تحميل المنتجات لإظهار الكميات المحدثة
                    loadWarehouseProducts();
                } else {
                    showNotification('error', response.message || '{{ __("حدث خطأ أثناء إضافة الكميات") }}');
                }
            },
            error: function(xhr) {
                var errorMessage = '{{ __("حدث خطأ أثناء إضافة الكميات") }}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showNotification('error', errorMessage);
            },
            complete: function() {
                // إعادة تعيين الزر
                $('#add-quantity-btn').html('<i class="fas fa-plus me-1"></i> {{ __("إضافة كمية") }}');
                $('#add-quantity-btn').prop('disabled', false);
            }
        });
    }
</script>
@endpush
