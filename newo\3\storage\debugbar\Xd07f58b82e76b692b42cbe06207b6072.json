{"__meta": {"id": "Xd07f58b82e76b692b42cbe06207b6072", "datetime": "2025-06-16 09:41:54", "utime": **********.415781, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750066912.894643, "end": **********.415819, "duration": 1.5211758613586426, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1750066912.894643, "relative_start": 0, "end": **********.24824, "relative_end": **********.24824, "duration": 1.3535969257354736, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.248265, "relative_start": 1.3536219596862793, "end": **********.415823, "relative_end": 4.0531158447265625e-06, "duration": 0.167557954788208, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43723832, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02901, "accumulated_duration_str": "29.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.360173, "duration": 0.02901, "duration_str": "29.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-208238306 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-208238306\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2044866464 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2044866464\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1554851964 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1554851964\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-985713372 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlY4TFhESnBVRGIrckhIeDRLeE4zaXc9PSIsInZhbHVlIjoiWlZQcmdTdmE4QUYyWXo4eUJqa0JFbDVyUUpUbnFGODZTVi9pcTZ3RkVIbm1XaGtPbENQM1dBQ3BuZFpYKzNRUHUvMmJKdTdwaGlTazNoQUJ6QkFqbGF5RUZpT09yMDJVREtOVWNFektyVk5ZWm05T2VoT2ZsWERLTTkvRld4dXFWNjRITjd2cXdyQ3pUeW9xeEtXc2l2M0pGWGlYTXRNTU1NWnRmYVBuRWpXT3BDZHdJRlV4K3U2OVEvWHVteHhzeXNUUkNyOTU5czVDWTNXYzlWcmRRcitqSmxLc1VsSFIrbER1RFFYdDVnWWNCa1BrcUJ4VHBhclBZVVdxN1lNSUVlQkw0MVlVWDRrWU55VVN4Mm0vcGNhb2VmN0tjcGdoTTB1ZllwM1VLWjV6b0htQmY4cVJLcTFCdHBuMGFWZ0NhTkFtai9zUFExNjBqakZDR0cva2NzN3llczJzTFhBc25YR3NpVFRjQWY3Y0ErdVdKaFR4Y21pMzdzTlJLSFc1ZUxqRlF6a2xvM0JzSVVPYmRZMUlXRzBmLzViMjlRaW03eUFYdkF6MnhpdzVLMUJqdXFXTW9xUUNtaStrZFlGT3hiaXdCV1pIcTgyU25Hb1hXTnFTNi9ySHJjamo3a1BhUHMxVDF6bDMvNm0xLzlvWlR0UFMvaTRRUFhIWGo2VWIiLCJtYWMiOiJhOWM4ZWRmMGJjYjhlMzI3YmM0Y2I0MzdkMmE2N2ZhODE4MmFhZTA1ZGQxMjNmNjEzYWRkMGQ5NTdhNTBjZTQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Imt5TEN0bU1obkpZdkJPODZyTGh3Tmc9PSIsInZhbHVlIjoianF4RHRUaUJUZWF5VWpEdDYyUG5FK25jUHFXQ0c4alV0cUQwOW5vN2IyOVVtcXpmK1hIVDN2MVlkZVZzem5OdTNOb2FjSDcydWxkckdqeFhDK3dpWGJaV3c2TXUyckpxNmlQSkFJeFoveG1heEFFc2h0aS9zOXJiNkNrMTViR2RrcXFVbGxETHQzcTdaNFhJRTBkOEZRVjJxMzUyVFFrS3NNTUdnWDJuZ2RGSy9oeUk4OXpHdjNlMFl2eDVsc1NCSmRhY1RSUmtnVmRBalBEUHVYOGVpZDJXTXR4UUp1TzJRTWs4eC9hWStIa3BjQURMY2dQRUk5Yis3ZjdFVkZRMWU4TGlwY3hMRFlNVm5TT0tHRk84bytEY0lHODc1MUJiNWduNWs1NzdrU0VCYUUrZVpweG9JUVBKUzFDcUdCbGZXTHd2Mk1BLzR0c2txblhHYm02by9aUjFLMGtNYXBLaFl1dGFEWFQ0SjZpVGpnaldhTkwrMUpiVGwvUmRheHdLT3Y0Yy9tMEJxZkNiekUrNXlORjZCV3hoSWp2azN1dDFDNWxKa1JuRTQwRUxkVXNHaklzczdNTlFiZDh6R1RSVjFLaXA5ZGpncVR1RVRxTEE2cy9wSUI0VkpUZ08yQVYzaTNyUUFUTUF6Q0RMaGNZeDRxakNiNDlIdGJ2MjBlVDMiLCJtYWMiOiJlZWZmMjI5OTg1ZDU3MzQ5MGY5YjJiYTg3YTJjOWMzN2VkNmYyZDc0NjdjMzY5ZDE2MzVkOWQ2MjNjMWY1NGYxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985713372\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1820334586 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820334586\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-602840941 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:41:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZtd2ZxWXRaM3hDaHFJem1CWE10QWc9PSIsInZhbHVlIjoibGhIVW1VcDdpR0dUckllUUpoV2RJc29ZT3pKWFJsTmVSVGhnVGRCSGdxS2xCQmp4SG83ZVRGVzRJMWdqSkZGSllnQVdXYXVDT3RUZmlvdzNIdWhMYU1IakJ3dFJxSGZkYlRMK3FLNlRIUG1GOEZnajdNc2NobFhrUGkrVTk4b1NxWFE2S0piLy8rQTNxNWF3UE45SU5nZHE2cWNHaTh1eGJGckEyZVcwZ0xYZXhCQUI0akVXNFRvZStHaDZzUENuNTFPaDVzb2k4WVR2TFMwdmM4VlJJTjFGeWZ6YjNNcjc2a1RLeGIyODV1eXd4K0lQZVpGQmdnNXlGNEpjS1VaYkJtUlJsM3ZLMkE4a3BRaHFXOUNvTnkvR20vbFYwVkRKKytUVVZ6aGtkZysyLzhMYTVxZDlPdlRrY1lwRklJRDdSWUpIclBYSUpPMFVHL1JlTGRtdDgyOVhxSVRhL1lSaFlmRk84TnFLVHBMSXBVWHlyZ3owbmVGNkpPK0lkRzV2Q1FscmFRdEkvMkgyUmp5aEhmVnlHb1dVQlh3UTRyQVhsczUwY0NVMkQvZVltWkxKSG1pT21lVjhMcDZLRkRPK2sySzhtVU5Uci9leG1lRXd0UXlkQ1Qrb05CL3MxdUhLbVZxelcvMnlhQURYekNRYS96N1ZhRzUyUVY1c3FtYnQiLCJtYWMiOiI3YTAwMmQzYjA0OTViMTA4ZDEyNzUxMWE5ZTUyYzZlZGYyYzAyYWNiY2ZlNDY1NjIzMjkxMjZhMGY2M2NmNTk2IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVDZ2J1c0k1VUtsLzNJMUVxbTd0akE9PSIsInZhbHVlIjoiTkRmMTI3aFY3T1lwT2d0bm9IL3BQQ0pLZUx3V1VUbWo5MjJBSzBkNlQ5Nmh1MHdwZWZMZml3VVF1MlNUdzFSSTduVDRkZzdZQmpodVduSnY1UjQ3T21QRlpLakRjK0VGVGN1L05xdVV4QXZsTmEyT2czLzdJK3ZTN0ROWm9HYk1KVENGS1AwWGVxSjNJVmZVZ1FTWFgzT25HOE1waDVlZkhaTnJ4N0FNbWtBeG5zajB3M2ZhRUo0TzZlVVNtQlJuTjJrc3dZL1Zwa0pBenVrUWczTHQrWitrNFFxWVY2aHZNK0RyZ1FYT3daZDh4SnJLeGlYNjRueG5OemVLNHc1eFFOU3k5YXIvQ3U2RjJ5bkl1T1hidUdTSEtXa045Mjl4Qi9kaG4rWGg0Smx2MFFVMU9KOVBQZmdsQTg0Q3NCN3U3RTg2UFhhSzBKVmw2ZXM2UkdCRDNFVUJjQ1JoQ21KWGU0UGNObFN1MGpWTm5PMVJrUUxlbXRiN0hIc1piVFNWVk5jalg1d2NrdzJGcThyVU9waGhxMDRqVUxycXZlN2g2SlJxRUl6Zm5FanJWbHVjZ2xJWGdtQjVZOVdYM2xQaUlEU1Y3c09CdEg3UXRUbjMrTEdoRHNCTE44M2FzRG10Mk02WUtuS2p2UU0xWXgvUExYRThkU1UrVXZvOFFyS1MiLCJtYWMiOiIyNDQwMDE2N2EzYmQ5MWMyZWVlZjgwMzNhOTEwNDAzMzg4MDg5ODU0YjJiZDZiMzdjZWZiYjgwODRlNjAwYWM3IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZtd2ZxWXRaM3hDaHFJem1CWE10QWc9PSIsInZhbHVlIjoibGhIVW1VcDdpR0dUckllUUpoV2RJc29ZT3pKWFJsTmVSVGhnVGRCSGdxS2xCQmp4SG83ZVRGVzRJMWdqSkZGSllnQVdXYXVDT3RUZmlvdzNIdWhMYU1IakJ3dFJxSGZkYlRMK3FLNlRIUG1GOEZnajdNc2NobFhrUGkrVTk4b1NxWFE2S0piLy8rQTNxNWF3UE45SU5nZHE2cWNHaTh1eGJGckEyZVcwZ0xYZXhCQUI0akVXNFRvZStHaDZzUENuNTFPaDVzb2k4WVR2TFMwdmM4VlJJTjFGeWZ6YjNNcjc2a1RLeGIyODV1eXd4K0lQZVpGQmdnNXlGNEpjS1VaYkJtUlJsM3ZLMkE4a3BRaHFXOUNvTnkvR20vbFYwVkRKKytUVVZ6aGtkZysyLzhMYTVxZDlPdlRrY1lwRklJRDdSWUpIclBYSUpPMFVHL1JlTGRtdDgyOVhxSVRhL1lSaFlmRk84TnFLVHBMSXBVWHlyZ3owbmVGNkpPK0lkRzV2Q1FscmFRdEkvMkgyUmp5aEhmVnlHb1dVQlh3UTRyQVhsczUwY0NVMkQvZVltWkxKSG1pT21lVjhMcDZLRkRPK2sySzhtVU5Uci9leG1lRXd0UXlkQ1Qrb05CL3MxdUhLbVZxelcvMnlhQURYekNRYS96N1ZhRzUyUVY1c3FtYnQiLCJtYWMiOiI3YTAwMmQzYjA0OTViMTA4ZDEyNzUxMWE5ZTUyYzZlZGYyYzAyYWNiY2ZlNDY1NjIzMjkxMjZhMGY2M2NmNTk2IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVDZ2J1c0k1VUtsLzNJMUVxbTd0akE9PSIsInZhbHVlIjoiTkRmMTI3aFY3T1lwT2d0bm9IL3BQQ0pLZUx3V1VUbWo5MjJBSzBkNlQ5Nmh1MHdwZWZMZml3VVF1MlNUdzFSSTduVDRkZzdZQmpodVduSnY1UjQ3T21QRlpLakRjK0VGVGN1L05xdVV4QXZsTmEyT2czLzdJK3ZTN0ROWm9HYk1KVENGS1AwWGVxSjNJVmZVZ1FTWFgzT25HOE1waDVlZkhaTnJ4N0FNbWtBeG5zajB3M2ZhRUo0TzZlVVNtQlJuTjJrc3dZL1Zwa0pBenVrUWczTHQrWitrNFFxWVY2aHZNK0RyZ1FYT3daZDh4SnJLeGlYNjRueG5OemVLNHc1eFFOU3k5YXIvQ3U2RjJ5bkl1T1hidUdTSEtXa045Mjl4Qi9kaG4rWGg0Smx2MFFVMU9KOVBQZmdsQTg0Q3NCN3U3RTg2UFhhSzBKVmw2ZXM2UkdCRDNFVUJjQ1JoQ21KWGU0UGNObFN1MGpWTm5PMVJrUUxlbXRiN0hIc1piVFNWVk5jalg1d2NrdzJGcThyVU9waGhxMDRqVUxycXZlN2g2SlJxRUl6Zm5FanJWbHVjZ2xJWGdtQjVZOVdYM2xQaUlEU1Y3c09CdEg3UXRUbjMrTEdoRHNCTE44M2FzRG10Mk02WUtuS2p2UU0xWXgvUExYRThkU1UrVXZvOFFyS1MiLCJtYWMiOiIyNDQwMDE2N2EzYmQ5MWMyZWVlZjgwMzNhOTEwNDAzMzg4MDg5ODU0YjJiZDZiMzdjZWZiYjgwODRlNjAwYWM3IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602840941\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-852878338 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852878338\", {\"maxDepth\":0})</script>\n"}}