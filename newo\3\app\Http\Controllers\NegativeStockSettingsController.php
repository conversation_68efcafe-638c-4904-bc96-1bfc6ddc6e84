<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Utility;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class NegativeStockSettingsController extends Controller
{
    /**
     * Display the negative stock settings page.
     */
    public function index()
    {
        if (Auth::user()->can('manage system settings')) {
            return view('settings.negative_stock_settings');
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Store the negative stock settings.
     */
    public function store(Request $request)
    {
        if (Auth::user()->can('manage system settings')) {
            
            $post = $request->all();
            
            // تحويل checkbox إلى on/off
            $post['allow_negative_stock'] = isset($post['allow_negative_stock']) ? 'on' : 'off';
            
            // حفظ الإعدادات
            foreach ($post as $key => $data) {
                if ($key !== '_token') {
                    $arr = [
                        $data,
                        $key,
                        Auth::user()->creatorId(),
                    ];

                    DB::insert(
                        'INSERT INTO settings (`value`, `name`, `created_by`) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)',
                        $arr
                    );
                }
            }

            return redirect()->back()->with('success', __('تم حفظ إعدادات المخزون السالب بنجاح.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }
}
