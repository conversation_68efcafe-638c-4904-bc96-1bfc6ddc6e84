{"__meta": {"id": "X4ce20e3a2146a7e232a9277592063a1d", "datetime": "2025-06-16 09:52:09", "utime": **********.308981, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750067527.701351, "end": **********.309028, "duration": 1.6076769828796387, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1750067527.701351, "relative_start": 0, "end": **********.014255, "relative_end": **********.014255, "duration": 1.3129041194915771, "duration_str": "1.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.014276, "relative_start": 1.312925100326538, "end": **********.309033, "relative_end": 5.0067901611328125e-06, "duration": 0.2947568893432617, "duration_str": "295ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48260440, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03591, "accumulated_duration_str": "35.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.135262, "duration": 0.02666, "duration_str": "26.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.241}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.19092, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.241, "width_percent": 5.319}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.243645, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 79.56, "width_percent": 4.595}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.251592, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.155, "width_percent": 3.871}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.266196, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 88.026, "width_percent": 8.633}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.283869, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 96.658, "width_percent": 3.342}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-594880906 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594880906\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.262878, "xdebug_link": null}]}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1524918006 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1524918006\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-924739289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-924739289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1552358541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1552358541\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1630903816 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1NMUV2aHR1ait5QW9pS3B6c25IN3c9PSIsInZhbHVlIjoia0M3eXFtMzBuWnZwd3o0QXNubWVKN0YvUklIN1FhRjdMbEZ0N3ZVYTFpQjI1L2ZtZFAxeGxURXBsWGY5eCt2K3BzUisrSXJaN1JLUHBaVVJMb0JHZFNaRkNIQjVyQmZYeFdnOG0ycDcya0xXYVZEbXhDVEVwUThuM3U2aUE2ZHNJdE9NblQ3d1A1YzFtMndNQWJ0K1RWLzJxRnB1NWdHcDgyQkZjZ1dWTGlzVWh3NTJmR2xVNFM0V0gvMUJTd3BzZC80eEpzQXdEZDYxWXk5STU1Q3VZa2ZWblAzV3FSTzZ3YWRlNFdOeDJZdHFEb1phWFFIYkdnRnFBYlFqYzJBam51S1o3emVDbUJEeWpkWUp5RmV0STlKN2VnY3lFd2xZNnRqRmtBdURZVk1wOVRPOERxQ2huMWt4cVV0RlNTdEhWS0d3N1BpTWYwWk9CNHhXRGNmQnJSS0diZ0xxaTdITUV2VGE5cE0vS3pQY2pwb3M2OHpLdSs2aU0xOXhsR011YWw4UHZWYlRoTUJteUk2aVY4SUx3M1B4WnRMZ1JhbStnUlA2cmlVMkNNR0ZpRDQwTDR0VGhzMVZZSDBIWXY1TnFUb2FXZEdGTFFVUWtxZEpOS2ZhTTlHL1FTcFVSV3VVMTVicFVDNWVlWDF4ZWI2dWJtTXBTODFUNEMydVhJQmsiLCJtYWMiOiJjMzVjNTIzOTk2YWZiNDI2N2RmYjViMDRjYjFlMzhkOGVlZDVkMWFjZWMwMDEyNjJjZDczZjA3YTllN2VjZjcxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFxZk84S1U1NTQ5aU9YTjVDMWp1alE9PSIsInZhbHVlIjoiNFkwdkQwa0FJeWVrN0poTHBGeTRaeXk5M0dvQ2gyZStRMWxvSlFobVUraE9COGFtZlJBNlRpZVVmaXZ2Smx0U0R0K0czUTRtR1hkY0Y0OE1RL1VhS2JLemlOS2lTdjlGT25iVDVIcGFBMUMrejJQYll0b2ZYSGhRNTRETUxmNmRxSEtCNzBnMUtaL2VKZExIVU1wb0xiclNkVmdFZFlHeFp5N0hEeTRHWVo3eEcwVHZFcEU1TXZnVWhZbzVDQ1p6NTgxN0pPNEtXamtHM3NtOSt3Ylhzc3M5L084Y3FzbmN6R3E5RWI0NUxGa0JoRWNINTRxRVEwWVltSEx1WkdaTDlkUU40OG1xeWdqYzc4Y2U3ZGZ5NnI2cVl6ak1rbm9CWlRGM0FBSHZYR1NZTmdOSmxUd2tXOVN6aHNFenY0Ykt5YWZuemkxQjJPRTdTTno0RUlqdU9OK3FySGk1NElkaG85WW12WXIybGp5Mzk5OXpyRk1rWHg4dUtFc0ZoNURGRVIrTzhUOFQ4N0Rmc0xVQU1OSFczWmhJVEFpd2JkK3BWUWN3b3l6UXhPbys3S1pUTTBhQVlsWDRGUWFuT0dWMlVHeWZxQzAwOExmSkpvcTI5bDVVZ0Q1Qk93aHQ1WSsxV2RGVEdGSlIzaFVCMmdXcm5pUzZaaExKSHlGMk5KOE4iLCJtYWMiOiIwZjVhODE1MDI1Y2JlNGY5ZmJlOTFmMGM3NWY3ZmQyMTEwMmJiOTAxNDU5ZmVhNzQyYmQxYzc5ZWQ3MDcwOGRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630903816\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1267020543 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267020543\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-327147243 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:52:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNDckNhYWNIRXZJaXNLM29UU0ZjK3c9PSIsInZhbHVlIjoicDNUQTkwSzZjTXZ1c2hjd3E5YUY0ZjlaYld1WXNjbGtTNUE1ay9FbEw0UDhSOGFMOC9RMmNPRnF2SXlrUHlyYTk4U29WSEl2VXRUYjdZTXQ0Q0xhOUZmRCtYTUhwZjdDdm96ZWpIOG5lbFpBS2Ntc3RoZEV4K2dBWGdWcFR6QW5lMXVyZDVEbS9QbUIyZEdWUThnUXZRRGlSZUY2by8wWWRrT29oZko1UDNwcDRiMVU0N0Y1a3BTWW4zVkZPQmhtYmVvVmZzSncwamxVSWdTdStCUVpuT1pGMC90UlhYY2dwWTlySEJhNEZITDVTK2d0Mk95STBhUlhacWo4N0prNytHVkNweVpZZFpzdnByamNUQTFzQ1BNemJ4aldpMXpEeVdTME9UekRvRzhBamJXYUJaL0ZHSjBiQmppekxLajhvT254MkU3ZEpIRHJ3YjFPZG5KVkt0UWo2a21BNnpia3o5cXNVOW5EYmI1N3JFV3ZWbXprQlk5VUtQdWx4YWNMelpiejMya3FzZHI3bnMzQ3dVNCtiaW5mRTVWcWRYSlVORHAwUkxiQzlGMEdUZE92Nkc0NlMwd3lGTlNFN0kyNmhhcHNwR1BIZnFadWEvNCtFU0g5ZHhvS1pRZkxBMURENEpqSWppbnpCOEM4U2tta2tnQlBNRnZWdEIvcFFvZGwiLCJtYWMiOiIwYzdiZDU3NDYzYTU2NGRkZDQ0ZDE4ZTg2ZTNiYWVjZTNkODI3NWJlOGRlNWM5M2YwZGQxMjQwYjczNmM3ZDdiIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:52:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InViazc5VXFsaHNDbHNkTjIvenB1SXc9PSIsInZhbHVlIjoibFdmZFRVVGI2dHpUMHFLdHRRcE5CdFdGMlQycnRkUVdOdFJkVkZKK3Z2am8vSDNkaHE5VjlqT2l3aDJaZldld0txS01jSU5DZ0d4azFtUDFocHUyaFROakM5NzJxWkJQeVN4OE5YV055UDhqRllnKzRybytqSTZ3ZXRzZGw4SG5iNHpNMnBITWpPTGxaOWlkdk9PYnN6MU9MamRRQ0w0L25Xd1g5NjNQV1p1ZmN3R1NHSGpZT0J3TnJGUTBvcVBDNUp2dWNFeDFhSjhnS2pGT2Z2eVg0RDdtaUdsbHowaXhQK2ViT2xBSkRnSkxxTTEzbzFETEVTaklDMHdBdzJIMnQzaDZ2OW5qMjZJWEtGaEsva1Z4L2hvZVRrRjRkbTFyYmpWR2lLOVg0TVpRaERaN2E0dEZ1UXBhSWk3WDdFN1Qvd2pmcmNhRTRYVXI2eXBhREtwUUZIcTREcWRpTjBHYzhaamJ5akdMazhOSkNsRmRUbGtVbGllRFMyZytIR2szVHBZUEJPQ3NjRkJ4NVE0a2NUbUMzMTBKTTM3T1kwdkhtYW8rcjNPbEJZVzA5dEhxWHhpNzJXc0Z0OG9oRG5UdHJYOEtZdG43LzNRVGFtQ3J5Qis1aW4yMDdrL29LTEtUYzZ6eTB2SFBMbGd5eWFEQWI4NWFlM3RKQmRselFCY04iLCJtYWMiOiI5NmZhZmFjNGVhZjliMmRmYTg2YjRmMzFlNzkwM2I2NjJiZTM2NjcyMzE5YzJmOTE3NGE2NjQ2ZTRhZWJiMDZmIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:52:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNDckNhYWNIRXZJaXNLM29UU0ZjK3c9PSIsInZhbHVlIjoicDNUQTkwSzZjTXZ1c2hjd3E5YUY0ZjlaYld1WXNjbGtTNUE1ay9FbEw0UDhSOGFMOC9RMmNPRnF2SXlrUHlyYTk4U29WSEl2VXRUYjdZTXQ0Q0xhOUZmRCtYTUhwZjdDdm96ZWpIOG5lbFpBS2Ntc3RoZEV4K2dBWGdWcFR6QW5lMXVyZDVEbS9QbUIyZEdWUThnUXZRRGlSZUY2by8wWWRrT29oZko1UDNwcDRiMVU0N0Y1a3BTWW4zVkZPQmhtYmVvVmZzSncwamxVSWdTdStCUVpuT1pGMC90UlhYY2dwWTlySEJhNEZITDVTK2d0Mk95STBhUlhacWo4N0prNytHVkNweVpZZFpzdnByamNUQTFzQ1BNemJ4aldpMXpEeVdTME9UekRvRzhBamJXYUJaL0ZHSjBiQmppekxLajhvT254MkU3ZEpIRHJ3YjFPZG5KVkt0UWo2a21BNnpia3o5cXNVOW5EYmI1N3JFV3ZWbXprQlk5VUtQdWx4YWNMelpiejMya3FzZHI3bnMzQ3dVNCtiaW5mRTVWcWRYSlVORHAwUkxiQzlGMEdUZE92Nkc0NlMwd3lGTlNFN0kyNmhhcHNwR1BIZnFadWEvNCtFU0g5ZHhvS1pRZkxBMURENEpqSWppbnpCOEM4U2tta2tnQlBNRnZWdEIvcFFvZGwiLCJtYWMiOiIwYzdiZDU3NDYzYTU2NGRkZDQ0ZDE4ZTg2ZTNiYWVjZTNkODI3NWJlOGRlNWM5M2YwZGQxMjQwYjczNmM3ZDdiIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:52:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InViazc5VXFsaHNDbHNkTjIvenB1SXc9PSIsInZhbHVlIjoibFdmZFRVVGI2dHpUMHFLdHRRcE5CdFdGMlQycnRkUVdOdFJkVkZKK3Z2am8vSDNkaHE5VjlqT2l3aDJaZldld0txS01jSU5DZ0d4azFtUDFocHUyaFROakM5NzJxWkJQeVN4OE5YV055UDhqRllnKzRybytqSTZ3ZXRzZGw4SG5iNHpNMnBITWpPTGxaOWlkdk9PYnN6MU9MamRRQ0w0L25Xd1g5NjNQV1p1ZmN3R1NHSGpZT0J3TnJGUTBvcVBDNUp2dWNFeDFhSjhnS2pGT2Z2eVg0RDdtaUdsbHowaXhQK2ViT2xBSkRnSkxxTTEzbzFETEVTaklDMHdBdzJIMnQzaDZ2OW5qMjZJWEtGaEsva1Z4L2hvZVRrRjRkbTFyYmpWR2lLOVg0TVpRaERaN2E0dEZ1UXBhSWk3WDdFN1Qvd2pmcmNhRTRYVXI2eXBhREtwUUZIcTREcWRpTjBHYzhaamJ5akdMazhOSkNsRmRUbGtVbGllRFMyZytIR2szVHBZUEJPQ3NjRkJ4NVE0a2NUbUMzMTBKTTM3T1kwdkhtYW8rcjNPbEJZVzA5dEhxWHhpNzJXc0Z0OG9oRG5UdHJYOEtZdG43LzNRVGFtQ3J5Qis1aW4yMDdrL29LTEtUYzZ6eTB2SFBMbGd5eWFEQWI4NWFlM3RKQmRselFCY04iLCJtYWMiOiI5NmZhZmFjNGVhZjliMmRmYTg2YjRmMzFlNzkwM2I2NjJiZTM2NjcyMzE5YzJmOTE3NGE2NjQ2ZTRhZWJiMDZmIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:52:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327147243\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-863771254 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863771254\", {\"maxDepth\":0})</script>\n"}}