{"__meta": {"id": "Xce3eb1f53fb166fc57b072e2bc460953", "datetime": "2025-06-16 08:50:33", "utime": **********.949374, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750063832.282999, "end": **********.949415, "duration": 1.6664159297943115, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": 1750063832.282999, "relative_start": 0, "end": **********.672098, "relative_end": **********.672098, "duration": 1.389098882675171, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.672121, "relative_start": 1.3891220092773438, "end": **********.949437, "relative_end": 2.193450927734375e-05, "duration": 0.2773158550262451, "duration_str": "277ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45211784, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.03216, "accumulated_duration_str": "32.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.777986, "duration": 0.02628, "duration_str": "26.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.716}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.835065, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.716, "width_percent": 4.913}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.891016, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 86.629, "width_percent": 7.4}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.921228, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.03, "width_percent": 5.97}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2063331148 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2063331148\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1348616508 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1348616508\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1357810504 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357810504\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1052603423 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1993 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwt%7C0%7C1960; _clsk=96tr34%7C1750063818910%7C1%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpHREFDSHNOTDRSUHV2d05YaWMxSmc9PSIsInZhbHVlIjoiLy9WVXVnelVmL0FpRkxJemhwNE1QUzNtSnlydUJNNitjMVhnc0UrTmkvTFBEbU5Lc1IzeVFYOW9kcFFaWllOT3d1QUs5WThDZThLVmpsd2luSlJRVXc0aDJlMFRtWHdLalYxTE44MzZCTVNISG9KWDdqakYvZWlHKzZJS1RYamZNY1JWejg4SGd1aGVKR25jRkhpeFdaOENjRjRKVTZmMG1raTBPdW1SR0lBK2llY2RkT0VHZjArSVZjZGRzK1lsR1ZodG54TzAyc3d6eVVFMlJHNHYxRW1nRmtoRjhBbFF1MWNWTG9XaG01Sko2NGVMc1NpMkk2dW5oS1h2SWxXSmlhUWJjalQ3TEozdFoyN2piNUw0OFRUNVJNSzV6QUpURDVPSldoRVdGSXRpYk4wOU5WTWwrSjRsVjNRc250YnFFczBFMG9iSnlVdkpVM3hUNGRrbGszTjFXd2FxU0lROUdoZkZQRVdyZ2YyY1VpaXd6c0xJQXd0VWVjN2FyeW5UV0h1OEV6aW5VMk1KalZPRVJTZkhxN3BZVkN6Y3c0dGRFaCtaYi85ZUQyd05VSGRwUEVnbUc3bnZDbW9CeTFUV0Y0UWptVzA2MG5qdEVqWFFpcWJueklSZzlsa2xJS3YxbGZWSWt2MUZwTWRNVVBkQ0JQbEVYNWw1cmprZWFuaksiLCJtYWMiOiJmYTM3OWU5ZDZjY2QzZTg5Yjg1MzI0YzRlOTdmOWZhMThkMzhiY2JjNDIyZWYxYmI0MzM2YzIxZjk2ZTk4ZTI4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdvS0RBcFkyOHpVY21KU3Y4YjNwUGc9PSIsInZhbHVlIjoiaDZsdks5eHRJd1QzdVBsMHZlNEVyT2hJODBDWnZjQVRvTTFRSnN6MGdsVHllZzBvTy8xbzN2SUJsUlFKRXgrNGgyanYyd0d0eEJKbFg0aTF3LzBkM1ZYL25kcFJrNGNidUxrY204Q3N3SVMxYS9YamZEaTJEV2FIRk9jYXpGMnNNbnRQTzB5YlhtRXl3akdxcmdMaVNEMXR5TmtZOUxJWnloYlBzVjFpNU8rM1RET2FQWWY1WW1KdGQxcWpvM2xvaytaY0xva3loN2V6SXVmb1cremtvZE1VYnl1Q2tmOXZVYlUxblhpL3Y3S0FFSGhPVWhpZG5LbjcxUkROelZEcXhCYXk4Y0ZvR1JpN1NBYVBNbEdzK2xCaVpFeWNmRzBiOWl5T245aHM5T3lnamlFRHduSmphcDhpNzhkU2g1QjFXVThTZDBJY1ZGSG40TFhTNXZRSFpJSktTU3EzalNoRTlJYnE4RkZrNlAya0RVNjlCdzI5dW91Q2NneHd4bUh5VHFDK2VaOVM0U0M4SWZMdVBOemZEU1dDdU9xdkVDRmwvN1A2UG5NYm5Ea2ErTDQrL3NHckNkeEczZzJVdzJLenpmTTNZZXRBMGk2VFJLQ1hEelU5WTNzN2YyS2puUDhXQzJVbWtSTytwT3htK1JGbzkzUlkvaWRQVDJHZDE1Qy8iLCJtYWMiOiIwNzU2OTQ5OTQ5NjQxOTRlMThmZjQ0NmU1ZWQzNGFiNTJiNzNhOGMzZGUwZjA5ZDczZGJhYWE1YmNkYzJhNWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052603423\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1338572219 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xy8o1OLZEDaYyV6WX9XzzYzbw5zHFfm46SvuTNQ8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338572219\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-190886294 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 08:50:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRwK29yU1ZxSzFBVjFISTRYZWcxZUE9PSIsInZhbHVlIjoiWDNsbkg0QmtVdExrbjVlUWpENUxuT0RvMzZySTBFMFBQejVJK3ljbXU0V3UyTnBmUUNoQVgwYkFVangxVk9SdjgrakNMYUtFTUcwTmFEVzJUZWN6Z0E3RTN4blp3WXh1L3hNZitSY3pPVEJSREYwKzdNV1JUTGNkc3QvUUNPcm43aEJGSHdvL1hrTDNDUE5zLzQ5RHVrbnd2NFRCZmpqeVYzU0FrUm1aakxDdHhBMXBwemhFUVZGYy8rTitobnpVMExzMk5sUHIySGhDY0xncjZqb1h1MGVBekd3ZUhnMEdvcEFmOXpuOWZodjY2V0xpL25CYlZaaFZuZ1N1NFdJZ3I1aFZNRURxenR5dXgvcGFObzZTblNCYmMwVDUwdnJFWWYvVy8zZzdNd1ZkSzJNSUNVVU1LNTVUY3FGM3dmTVJkQ25USzVDYkVnY0RXR2NKQ3RjMjFGQjNxTFlDNXpNZzdrY3h2SVBwTisxODViMTJiNHhqZWpHbE92Y1JGZTM2bEdnVVNKNko1RzQ1N1oydDFIU0srUW1UaUIzcEcwSmlMY0swclB6Qk5VV1l3QjZwcnRIRUNGNG51Zk53WG1mbkthU2FLYlVaSzcwanRYQzVxb0NvNTYzZ3YrM2xmYWdWYWliVnpDQ1Z0R242YytuMTcxM1FidW9BenFvY3EzcnQiLCJtYWMiOiI0YTE5ZGQzNTIzY2VhNzYzYTUxYjA5NzUzY2U4ZjZkMTJkNjFlMmM0NzFiOTdmZmNkMWVhMDJiMTg5MGJlOGRkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImUzREQ4cWlkcWpVMHp2OVMzUkF3emc9PSIsInZhbHVlIjoiWmdBeGdVcnpiR1M1M0VydU5IeHR6S3hMVWFrVm1SbjZvaVpmSWx0RlZUMzVEVzNuUk9GM2lya2prNnJvSWxvVmFBamJhN0Q2bFRmVkpwZEsycEpZa0wzRGFJSHlQK0VQbUhaM0FkQTJ4NFNaa1hsajN0bU50UFhoMUhPU1lXcGh1akZZSFZQazc4L1gxMEMvRnp6aWtLUngxR2MyN2FhWTF5RHl2TjNTeUhsKzJRMTA4dzN1aGtzYU5lNUlKWXRTekY1MnpTc2tqaWJYbWpkaUNwVkR6TS9WYVVlQlppUG5oM3plMXI2MEhRYVB3UG9jVzY2aEJLUEVjNmRtdFFjTTBFanFRcEo2Sml0R1N0SjJwcnpYTVBUZ3FIZ3I4SDhsc0JMRVhyMmxhZGhEUEVkaERPMHlZeEpOWFB4RDRQK1J0NE15eWJnTS9wbU5KSFdpWGxRNXVKK3FTcVZQalFIU0FIMjk2Sk9GVUNjR0JVVjdReVZxTTVUQTRMSjZwZENPZFZWOVk2VkVZZlN3Ukh4eTZsT29oclo0ZVpDRW8ybFV5Y1hBeG9paG9mVmVxWlNIYUdZcmhVd1VHK3NILzFuWjd5WWJJY01CZCtMc3BYd1RDK2hNbnQ5cnVCMVZRR2VPUjN3MU45YlVGSkhIL08zazEveG80ZVpKL1FqRmJEN3EiLCJtYWMiOiI3MzlhNDAzN2FhYzgyOTRjNmUzNWMwYTcyZTVhMGJlOGI1Njk5YzI4YTIzNTQyZTdhOTQ0YjlkMmUyZGZmMTgwIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 10:50:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRwK29yU1ZxSzFBVjFISTRYZWcxZUE9PSIsInZhbHVlIjoiWDNsbkg0QmtVdExrbjVlUWpENUxuT0RvMzZySTBFMFBQejVJK3ljbXU0V3UyTnBmUUNoQVgwYkFVangxVk9SdjgrakNMYUtFTUcwTmFEVzJUZWN6Z0E3RTN4blp3WXh1L3hNZitSY3pPVEJSREYwKzdNV1JUTGNkc3QvUUNPcm43aEJGSHdvL1hrTDNDUE5zLzQ5RHVrbnd2NFRCZmpqeVYzU0FrUm1aakxDdHhBMXBwemhFUVZGYy8rTitobnpVMExzMk5sUHIySGhDY0xncjZqb1h1MGVBekd3ZUhnMEdvcEFmOXpuOWZodjY2V0xpL25CYlZaaFZuZ1N1NFdJZ3I1aFZNRURxenR5dXgvcGFObzZTblNCYmMwVDUwdnJFWWYvVy8zZzdNd1ZkSzJNSUNVVU1LNTVUY3FGM3dmTVJkQ25USzVDYkVnY0RXR2NKQ3RjMjFGQjNxTFlDNXpNZzdrY3h2SVBwTisxODViMTJiNHhqZWpHbE92Y1JGZTM2bEdnVVNKNko1RzQ1N1oydDFIU0srUW1UaUIzcEcwSmlMY0swclB6Qk5VV1l3QjZwcnRIRUNGNG51Zk53WG1mbkthU2FLYlVaSzcwanRYQzVxb0NvNTYzZ3YrM2xmYWdWYWliVnpDQ1Z0R242YytuMTcxM1FidW9BenFvY3EzcnQiLCJtYWMiOiI0YTE5ZGQzNTIzY2VhNzYzYTUxYjA5NzUzY2U4ZjZkMTJkNjFlMmM0NzFiOTdmZmNkMWVhMDJiMTg5MGJlOGRkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImUzREQ4cWlkcWpVMHp2OVMzUkF3emc9PSIsInZhbHVlIjoiWmdBeGdVcnpiR1M1M0VydU5IeHR6S3hMVWFrVm1SbjZvaVpmSWx0RlZUMzVEVzNuUk9GM2lya2prNnJvSWxvVmFBamJhN0Q2bFRmVkpwZEsycEpZa0wzRGFJSHlQK0VQbUhaM0FkQTJ4NFNaa1hsajN0bU50UFhoMUhPU1lXcGh1akZZSFZQazc4L1gxMEMvRnp6aWtLUngxR2MyN2FhWTF5RHl2TjNTeUhsKzJRMTA4dzN1aGtzYU5lNUlKWXRTekY1MnpTc2tqaWJYbWpkaUNwVkR6TS9WYVVlQlppUG5oM3plMXI2MEhRYVB3UG9jVzY2aEJLUEVjNmRtdFFjTTBFanFRcEo2Sml0R1N0SjJwcnpYTVBUZ3FIZ3I4SDhsc0JMRVhyMmxhZGhEUEVkaERPMHlZeEpOWFB4RDRQK1J0NE15eWJnTS9wbU5KSFdpWGxRNXVKK3FTcVZQalFIU0FIMjk2Sk9GVUNjR0JVVjdReVZxTTVUQTRMSjZwZENPZFZWOVk2VkVZZlN3Ukh4eTZsT29oclo0ZVpDRW8ybFV5Y1hBeG9paG9mVmVxWlNIYUdZcmhVd1VHK3NILzFuWjd5WWJJY01CZCtMc3BYd1RDK2hNbnQ5cnVCMVZRR2VPUjN3MU45YlVGSkhIL08zazEveG80ZVpKL1FqRmJEN3EiLCJtYWMiOiI3MzlhNDAzN2FhYzgyOTRjNmUzNWMwYTcyZTVhMGJlOGI1Njk5YzI4YTIzNTQyZTdhOTQ0YjlkMmUyZGZmMTgwIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 10:50:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190886294\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-45592745 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sqOH7VBxhdpNqHWftSvKyk9bDY3RJ3a4BkEknnej</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45592745\", {\"maxDepth\":0})</script>\n"}}