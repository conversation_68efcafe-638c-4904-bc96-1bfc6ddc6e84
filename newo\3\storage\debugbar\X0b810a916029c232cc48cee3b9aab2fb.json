{"__meta": {"id": "X0b810a916029c232cc48cee3b9aab2fb", "datetime": "2025-06-16 09:41:56", "utime": **********.538473, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750066914.815482, "end": **********.538524, "duration": 1.7230420112609863, "duration_str": "1.72s", "measures": [{"label": "Booting", "start": 1750066914.815482, "relative_start": 0, "end": **********.307855, "relative_end": **********.307855, "duration": 1.492372989654541, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.307889, "relative_start": 1.4924070835113525, "end": **********.538528, "relative_end": 4.0531158447265625e-06, "duration": 0.23063898086547852, "duration_str": "231ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45243568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.029079999999999998, "accumulated_duration_str": "29.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.437469, "duration": 0.02754, "duration_str": "27.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.704}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.502455, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.704, "width_percent": 5.296}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-236475947 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-236475947\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1825957796 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1825957796\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1027488632 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1027488632\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1397067595 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1pubyvl%7C2%7Cfwt%7C0%7C1993; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1s0mt6n%7C1750063901378%7C2%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZtd2ZxWXRaM3hDaHFJem1CWE10QWc9PSIsInZhbHVlIjoibGhIVW1VcDdpR0dUckllUUpoV2RJc29ZT3pKWFJsTmVSVGhnVGRCSGdxS2xCQmp4SG83ZVRGVzRJMWdqSkZGSllnQVdXYXVDT3RUZmlvdzNIdWhMYU1IakJ3dFJxSGZkYlRMK3FLNlRIUG1GOEZnajdNc2NobFhrUGkrVTk4b1NxWFE2S0piLy8rQTNxNWF3UE45SU5nZHE2cWNHaTh1eGJGckEyZVcwZ0xYZXhCQUI0akVXNFRvZStHaDZzUENuNTFPaDVzb2k4WVR2TFMwdmM4VlJJTjFGeWZ6YjNNcjc2a1RLeGIyODV1eXd4K0lQZVpGQmdnNXlGNEpjS1VaYkJtUlJsM3ZLMkE4a3BRaHFXOUNvTnkvR20vbFYwVkRKKytUVVZ6aGtkZysyLzhMYTVxZDlPdlRrY1lwRklJRDdSWUpIclBYSUpPMFVHL1JlTGRtdDgyOVhxSVRhL1lSaFlmRk84TnFLVHBMSXBVWHlyZ3owbmVGNkpPK0lkRzV2Q1FscmFRdEkvMkgyUmp5aEhmVnlHb1dVQlh3UTRyQVhsczUwY0NVMkQvZVltWkxKSG1pT21lVjhMcDZLRkRPK2sySzhtVU5Uci9leG1lRXd0UXlkQ1Qrb05CL3MxdUhLbVZxelcvMnlhQURYekNRYS96N1ZhRzUyUVY1c3FtYnQiLCJtYWMiOiI3YTAwMmQzYjA0OTViMTA4ZDEyNzUxMWE5ZTUyYzZlZGYyYzAyYWNiY2ZlNDY1NjIzMjkxMjZhMGY2M2NmNTk2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVDZ2J1c0k1VUtsLzNJMUVxbTd0akE9PSIsInZhbHVlIjoiTkRmMTI3aFY3T1lwT2d0bm9IL3BQQ0pLZUx3V1VUbWo5MjJBSzBkNlQ5Nmh1MHdwZWZMZml3VVF1MlNUdzFSSTduVDRkZzdZQmpodVduSnY1UjQ3T21QRlpLakRjK0VGVGN1L05xdVV4QXZsTmEyT2czLzdJK3ZTN0ROWm9HYk1KVENGS1AwWGVxSjNJVmZVZ1FTWFgzT25HOE1waDVlZkhaTnJ4N0FNbWtBeG5zajB3M2ZhRUo0TzZlVVNtQlJuTjJrc3dZL1Zwa0pBenVrUWczTHQrWitrNFFxWVY2aHZNK0RyZ1FYT3daZDh4SnJLeGlYNjRueG5OemVLNHc1eFFOU3k5YXIvQ3U2RjJ5bkl1T1hidUdTSEtXa045Mjl4Qi9kaG4rWGg0Smx2MFFVMU9KOVBQZmdsQTg0Q3NCN3U3RTg2UFhhSzBKVmw2ZXM2UkdCRDNFVUJjQ1JoQ21KWGU0UGNObFN1MGpWTm5PMVJrUUxlbXRiN0hIc1piVFNWVk5jalg1d2NrdzJGcThyVU9waGhxMDRqVUxycXZlN2g2SlJxRUl6Zm5FanJWbHVjZ2xJWGdtQjVZOVdYM2xQaUlEU1Y3c09CdEg3UXRUbjMrTEdoRHNCTE44M2FzRG10Mk02WUtuS2p2UU0xWXgvUExYRThkU1UrVXZvOFFyS1MiLCJtYWMiOiIyNDQwMDE2N2EzYmQ5MWMyZWVlZjgwMzNhOTEwNDAzMzg4MDg5ODU0YjJiZDZiMzdjZWZiYjgwODRlNjAwYWM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397067595\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2100948258 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cGwlbBhi7aNwDbkjNSOuRWGAWsmGFaoIWLkhRBXC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100948258\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1058342805 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 09:41:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjU3MC94YnplOVhncWcrOTlQeGFvUmc9PSIsInZhbHVlIjoiMW9iUlY4dzdGYzR5d1ZoODVKdzB4cytvdTVBQjFiekIzRndlVklDcFllVmRyYTJVSzBuQTBzZGRVdFVVcjQzZlBPNzNtL0RVYTQyMHBUK042eEFMRnMwelVLUS95em5iQWhsaDk4a2ZJRXBVdlRBQmZSSlJUVnFwbFZscFc5dHZlbXExTWpIb1lOUlJnWmsyRG11WGFoTFptV1VJYTdUamVlYUtJRE5FR0VxakN6djQyVmxQUzRvdUtCQXQwRjl2a2dSS3FHYzVZM2FlY1A4S2pXTTdiTVpwRFl3bzRTdGxMRStZVElOM1BJMjlNM3dKNzVmMUNFcUEzblJLdTVvRWF0RUlCN0cvd2laVm1LQW1FMVl3bUUrN3IrNUlzZDFvcFdCcWc2VjNnSkNVWmxqN3g4aFdWTVl0SndGSTBPTFVrK1ZjVnZFYnp4UTNFYU5JeXM0Z1hvSnVNdVB6RWI2cGEvaWtiK1JtUlpScnZDV3lVcDhzcEJMbmwxbVNSZzZXdDJYdERWYkdXUU1iN3RmQnRkM3NVVWtZeW4xSGlRc3M5dnlCYm14WHdPdEtJRFMwM2V3OEJrbkViNkhyaW9nNXc0L0YxSzdsRlAzMGQ5R01UYlJ5UFRwWERya00zVjZJMS9Fbkx0dks2RE9zcytKZ1hFVkdnQWV4blJEdEYvVGUiLCJtYWMiOiI4MDI2M2UxNTM3OTIyZjlmY2QyMjhkZmZmOTJiNWU1MmEyNWMyM2UzYmNjYWU2MDI5ODYzMzNiMWZkZjI0ZDg0IiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNONXI5eEk3cVBsVXpwZW9GTU9TV3c9PSIsInZhbHVlIjoiQUs1UUFGTERQb2orK281ejlVYWI2L0ZOOUFwZm9VczRiRnhuYXB0NVI4aUxUMnk5VEVHMTdWeGtrS1lsMzFMbjFnMDBCeTRsMTF1cGlneCtlcTJXUFJUTjFobEFrdi9ZakpPN3poVW5qU2tYWm5hTHZHS0VxSzM2VzZTa1B6UzlmS0ZXRXRMd2g5QU5TWGcxT2hDR0hYSUpoYVdkUkxHc1hLOXIxbkdQUXBYV1VuckhrZWYzSHJsVFZtQ2oyNUhjclphSTZLbjFnVERDbDd4em9BOHl3TlFCd0dVbVp0MTdJR1lUNE9PU3lWZ01ucENnSG5WT2lUNm16VWpScndpYktVaE4vRnQxTXRlWWh6NnNTMkxyZXp4NkdpL0Z6b2VaSVkyangrbWxiT1FQWHRIRlphbG04MjN2R1RnbkZoMXdUc0lLTWxlb1RjTGw5MWxjL2pQaW5OUUxKZm1venkxbXMyVHk2Q25vUTRueC8yWjdMeVVhVmtBbUMrVys1MzR5cEhNY1V2Q3I2YnY3bEhDN0RiVjBRZDVFUVcya0s4aTNTN0xnMXhscHZuQm5wYnVDRzhQNTNtUjFXMVdXaG5zTDByOWEwZk1sWWtkSzZHRUptbmhvR21lZE1CUStUUjhHVHdSVWE1dUw5VmlyNW5VTDkxRERVV0JSeGR2SFRIS0wiLCJtYWMiOiI1OWE4YTI4ZWNiNzA1ODZjNzAxZjE4ZTgzOTJjNTRjZmYyNjYxZmQwZTNiN2FkMmY4NjRlMjAyMmM4OTI1M2FkIiwidGFnIjoiIn0%3D; expires=Mon, 16 Jun 2025 11:41:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjU3MC94YnplOVhncWcrOTlQeGFvUmc9PSIsInZhbHVlIjoiMW9iUlY4dzdGYzR5d1ZoODVKdzB4cytvdTVBQjFiekIzRndlVklDcFllVmRyYTJVSzBuQTBzZGRVdFVVcjQzZlBPNzNtL0RVYTQyMHBUK042eEFMRnMwelVLUS95em5iQWhsaDk4a2ZJRXBVdlRBQmZSSlJUVnFwbFZscFc5dHZlbXExTWpIb1lOUlJnWmsyRG11WGFoTFptV1VJYTdUamVlYUtJRE5FR0VxakN6djQyVmxQUzRvdUtCQXQwRjl2a2dSS3FHYzVZM2FlY1A4S2pXTTdiTVpwRFl3bzRTdGxMRStZVElOM1BJMjlNM3dKNzVmMUNFcUEzblJLdTVvRWF0RUlCN0cvd2laVm1LQW1FMVl3bUUrN3IrNUlzZDFvcFdCcWc2VjNnSkNVWmxqN3g4aFdWTVl0SndGSTBPTFVrK1ZjVnZFYnp4UTNFYU5JeXM0Z1hvSnVNdVB6RWI2cGEvaWtiK1JtUlpScnZDV3lVcDhzcEJMbmwxbVNSZzZXdDJYdERWYkdXUU1iN3RmQnRkM3NVVWtZeW4xSGlRc3M5dnlCYm14WHdPdEtJRFMwM2V3OEJrbkViNkhyaW9nNXc0L0YxSzdsRlAzMGQ5R01UYlJ5UFRwWERya00zVjZJMS9Fbkx0dks2RE9zcytKZ1hFVkdnQWV4blJEdEYvVGUiLCJtYWMiOiI4MDI2M2UxNTM3OTIyZjlmY2QyMjhkZmZmOTJiNWU1MmEyNWMyM2UzYmNjYWU2MDI5ODYzMzNiMWZkZjI0ZDg0IiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNONXI5eEk3cVBsVXpwZW9GTU9TV3c9PSIsInZhbHVlIjoiQUs1UUFGTERQb2orK281ejlVYWI2L0ZOOUFwZm9VczRiRnhuYXB0NVI4aUxUMnk5VEVHMTdWeGtrS1lsMzFMbjFnMDBCeTRsMTF1cGlneCtlcTJXUFJUTjFobEFrdi9ZakpPN3poVW5qU2tYWm5hTHZHS0VxSzM2VzZTa1B6UzlmS0ZXRXRMd2g5QU5TWGcxT2hDR0hYSUpoYVdkUkxHc1hLOXIxbkdQUXBYV1VuckhrZWYzSHJsVFZtQ2oyNUhjclphSTZLbjFnVERDbDd4em9BOHl3TlFCd0dVbVp0MTdJR1lUNE9PU3lWZ01ucENnSG5WT2lUNm16VWpScndpYktVaE4vRnQxTXRlWWh6NnNTMkxyZXp4NkdpL0Z6b2VaSVkyangrbWxiT1FQWHRIRlphbG04MjN2R1RnbkZoMXdUc0lLTWxlb1RjTGw5MWxjL2pQaW5OUUxKZm1venkxbXMyVHk2Q25vUTRueC8yWjdMeVVhVmtBbUMrVys1MzR5cEhNY1V2Q3I2YnY3bEhDN0RiVjBRZDVFUVcya0s4aTNTN0xnMXhscHZuQm5wYnVDRzhQNTNtUjFXMVdXaG5zTDByOWEwZk1sWWtkSzZHRUptbmhvR21lZE1CUStUUjhHVHdSVWE1dUw5VmlyNW5VTDkxRERVV0JSeGR2SFRIS0wiLCJtYWMiOiI1OWE4YTI4ZWNiNzA1ODZjNzAxZjE4ZTgzOTJjNTRjZmYyNjYxZmQwZTNiN2FkMmY4NjRlMjAyMmM4OTI1M2FkIiwidGFnIjoiIn0%3D; expires=Mon, 16-Jun-2025 11:41:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058342805\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1290801390 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">crN9tWuH9dusvikqfFokVSYu3maREcwOTSdzV2M3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290801390\", {\"maxDepth\":0})</script>\n"}}